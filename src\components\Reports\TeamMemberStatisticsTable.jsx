import { useEffect, useMemo, useRef, useState } from "react";
import {
    useFilters,
    useGlobalFilter,
    usePagination,
    useSortBy,
    useTable,
} from "react-table";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import { Button, Col, Row, Table } from "react-bootstrap";
import { BsFillCaretDownFill } from "react-icons/bs";
import {Link, useNavigate} from "react-router-dom";
import { FaCalendarAlt } from "react-icons/fa";
import { TiArrowForward } from "react-icons/ti";
import DatePicker from "react-datepicker";
import { toast } from "react-toastify";
import {getTeamMemberStatistics} from "../../services/reports/get-leads-reports.api";
import {useTranslatedColumns} from "./ColumnsForTables.module";
import PaginationRecordsForReports from "./PaginationRecordsForReports";
import {IoCaretBack} from "react-icons/io5";
import { useTranslation } from 'react-i18next';

const parseDate = (date) => {
    if (!date) return null;
    return new Date(date).toLocaleDateString('en-GB');
};

const formatDate = (date) => {
    if (!date) return null;
    return new Date(date).toISOString().split(".")[0];
};

function GlobalFilter({
                          setGlobalFilter,
                          fetchInitialData,
                          recordsPerPage,
                          currentPage,
                          startDate,
                          setStartDate,
                          endDate,
                          setEndDate,
                          selectedTM,
                          setLocalDates
                      }) {
    const { t } = useTranslation();
    
    const applyFilters = async () => {
        const params = {
            from: formatDate(startDate),
            to: formatDate(endDate),
            member: selectedTM?.id,
            recordsPerPage,
            currentPage,
        };
        await fetchInitialData(params);
    };

    const clearFilters = () => {
        setGlobalFilter("");
        setStartDate(null);
        setEndDate(null);
        fetchInitialData({ currentPage, recordsPerPage, member: selectedTM?.id });
    };

    return (
        <Row className="mb-3">
            <Col md={12} className="d-flex justify-content-between align-items-center">
                <div className="d-flex align-items-center gap-2">
                    <div className="date-range-picker d-flex align-items-center gap-2">
                        <span>{t('filters.dateRange')}</span>
                        <div className="d-flex align-items-center gap-2">
                            <DatePicker
                                selected={startDate}
                                onChange={(date) => {
                                    setStartDate(date)
                                    setLocalDates(prev => ({...prev, from: date}))
                                }}
                                selectsStart
                                startDate={startDate}
                                endDate={endDate}
                                placeholderText={t('filters.from')}
                                className="form-control"
                            />
                            <FaCalendarAlt />
                        </div>
                        <div className="d-flex align-items-center gap-2">
                            <DatePicker
                                selected={endDate}
                                onChange={(date) => {
                                    setEndDate(date)
                                    setLocalDates(prev => ({...prev, to: date}))
                                }}
                                selectsEnd
                                startDate={startDate}
                                endDate={endDate}
                                minDate={startDate}
                                placeholderText={t('filters.to')}
                                className="form-control"
                            />
                            <FaCalendarAlt />
                        </div>
                    </div>
                </div>
                <div className="d-flex gap-2">
                    <Button onClick={applyFilters} className="px-3 py-2 apply-btn">
                        {t('filters.apply')}
                    </Button>
                    <Button onClick={clearFilters} className="rounded-pill clear-btn px-3 mx-2">
                        {t('filters.clear')}
                    </Button>
                    <div className="d-flex justify-content-between align-items-center rounded-pill bg-dark text-white px-3 py-1 fs-6" role="button">
                        <TiArrowForward />
                        <div>{t('filters.export')}</div>
                    </div>
                </div>
            </Col>
        </Row>
    );
}

function DefaultColumnFilter({
                                 column: { filterValue, preFilteredRows, setFilter },
                             }) {
    const { t } = useTranslation();
    const count = preFilteredRows.length;

    return (
        <input
            className="form-control"
            value={filterValue || ""}
            onChange={(e) => {
                setFilter(e.target.value || undefined);
            }}
            placeholder={t('filters.searchRecords', { count })}
        />
    );
}

const TeamMemberStatisticsTable = ({selectedTM, startDate, endDate, setStartDate, setEndDate, setShowStatistics}) => {
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(10);
    const [data, setData] = useState([]);
    const [paginationLinks, setPaginationLinks] = useState([]);
    const [total, setTotal] = useState(0);
    const [recordsToDisplay, setRecordsToDisplay] = useState(10); // Default value
    const fetchMemberStatisticsAbortController = useRef(null);
    const [localDates, setLocalDates] = useState({from: startDate, to: endDate});
    const fetchInitialData = async (params) => {
        try {
            setLoading(true);

            // Abort previous fetch if any
            if (fetchMemberStatisticsAbortController.current) {
                fetchMemberStatisticsAbortController.current.abort();
            }
            fetchMemberStatisticsAbortController.current = new AbortController();
            const signal = fetchMemberStatisticsAbortController.current.signal;
            // Fetch data
            const response = await getTeamMemberStatistics({ signal, params });

            // Handle response
            if (response?.success) {
                if (
                    response?.result === "there is no data" ||
                    (Array.isArray(response?.result) && response?.result.length === 0)
                ) {
                    // Handle no data case
                    setData([]);
                    setTotal(0);
                    setRecordsToDisplay(0);
                    setPaginationLinks([]);
                    toast.info("No data available for the selected filters.", {
                        position: "bottom-right",
                        theme: "dark",
                    });
                } else {
                    const { data, current_page, per_page, links, total, to } =
                        response.result;
                    setData(data);
                    setCurrentPage(current_page);
                    setRecordsPerPage(per_page);
                    setPaginationLinks(links);
                    setTotal(total);
                    setRecordsToDisplay(to);
                    setLocalDates({from: startDate, to: endDate})
                }
            }
            setLoading(false);
        } catch (error) {
            console.error("Error fetching initial data:", error);
            setLoading(false);
        }
    };

    useEffect(() => {
        if (!selectedTM?.id) {
            return;
        }
        fetchInitialData({ currentPage, recordsPerPage, member: selectedTM?.id });
    }, [selectedTM?.id, selectedTM?.name]);

    const defaultColumn = useMemo(
        () => ({
            Filter: DefaultColumnFilter,
        }),
        [],
    );

    const { statisticsForTMColumns } = useTranslatedColumns();

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        prepareRow,
        preGlobalFilteredRows,
        setGlobalFilter,
        setPageSize,
        rows,
        state: { globalFilter },
    } = useTable(
        {
            columns: statisticsForTMColumns,
            data: data,
            defaultColumn,
            initialState: { pageIndex: currentPage - 1, pageSize: recordsPerPage },
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination,
    );

    const handlePageChange = async (url) => {
        const params = {
            from: formatDate(startDate),
            to: formatDate(endDate),
            member: selectedTM?.id,
            url,
            recordsPerPage,
        };
        await fetchInitialData(params);
    };

    const handlePageSizeChange = async (size) => {
        setRecordsPerPage(size);
        setPageSize(size);
        const params = {
            from: formatDate(startDate),
            to: formatDate(endDate),
            member: selectedTM?.id,
            currentPage,
            recordsPerPage: size,
        };
        await fetchInitialData(params);
    };

    return loading ? (
        <FetchingDataLoading className={"content-container"} />
    ) : (
        <>
            <div className={"content-container position-relative"}>
                <div
                    role={"button"}
                    onClick={() => setShowStatistics(false)}
                    title={"Back To Team Members"}
                    className={"go-back-btn"}
                >
                    <IoCaretBack
                        color={"#92C020"}
                        className={"bg-dark rounded-circle p-1"}
                        size={35}
                    />
                </div>
                <center>
                    <h2>Statistics for <span className={"fw-bold"}>{selectedTM?.name}</span></h2>
                    <div className={"fs-6 fw-bold"}>
                        {startDate ? `from ${parseDate(localDates?.from)}` : null} {endDate ? `to ${parseDate(localDates?.to)}` : null}
                    </div>
                </center>
            </div>
            <div className={"content-container"}>
                <GlobalFilter
                    preGlobalFilteredRows={preGlobalFilteredRows}
                    globalFilter={globalFilter}
                    setGlobalFilter={setGlobalFilter}
                    // handleFilterByDuration={handleFilterByDuration}
                    fetchInitialData={fetchInitialData}
                    startDate={startDate}
                    endDate={endDate}
                    setStartDate={setStartDate}
                    setEndDate={setEndDate}
                    recordsPerPage={recordsPerPage}
                    currentPage={currentPage}
                    selectedTM={selectedTM}
                    setLocalDates={setLocalDates}
                />
            </div>
            <div className={"all-leads-table px-2"}>
                <Table
                    responsive={"xl"}
                    className="table text-center position-relative"
                    {...getTableProps()}
                >
                    {loading ? (
                        <FetchingDataLoading/>
                    ) : (
                        <>
                            <thead>
                            {headerGroups?.map((headerGroup, index) => (
                                <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                                    {headerGroup.headers?.map((column, j) => (
                                        <th
                                            {...column.getHeaderProps(
                                                column.getSortByToggleProps(),
                                            )}
                                            key={j}
                                        >
                                            {column.render("Header")}
                                            <span>
                          {column.isSorted ? (
                              column.isSortedDesc ? (
                                  " 🔽"
                              ) : (
                                  " 🔼"
                              )
                          ) : (
                              <> {column.accessor && <BsFillCaretDownFill/>}</>
                          )}
                        </span>
                                        </th>
                                    ))}
                                </tr>
                            ))}
                            </thead>
                            <tbody {...getTableBodyProps()}>
                            {rows?.map((row) => {
                                prepareRow(row);
                                return (
                                    <tr
                                        {...row.getRowProps()}
                                        className={"client-table-row filter-table-rows"}
                                        style={{cursor: "default"}}
                                        key={row.original.id}
                                    >
                                        {row?.cells?.map((cell, j) => {
                                            return (
                                                <td {...cell.getCellProps()} key={j}>
                                                        {cell.render("Cell")}
                                                </td>
                                            );
                                        })}
                                    </tr>
                                );
                            })}
                            </tbody>
                        </>
                    )}
                </Table>
                <PaginationRecordsForReports
                    onPageChange={handlePageChange}
                    links={paginationLinks}
                    handlePageSizeChange={handlePageSizeChange}
                    per_page={recordsPerPage}
                    to={recordsToDisplay}
                    total={total}
                />
            </div>
        </>
    );
};

export default TeamMemberStatisticsTable;
