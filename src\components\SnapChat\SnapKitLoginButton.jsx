import React, { useState, useEffect } from "react";

const SnapKitLoginButton = () => {
  const [displayName, setDisplayName] = useState("");
  const [bitmojiSrc, setBitmojiSrc] = useState("");
  const [externalId, setExternalId] = useState("");
  useEffect(() => {
    // Function to handle Snap Kit initialization
    const snapKitInit = () => {
      if (window.snap && window.snap.loginkit) {
        // Mount Login Button
        const loginButtonIconId = "my-login-button-target";
        window.snap.loginkit.mountButton(loginButtonIconId, {
          clientId: process.env.REACT_APP_SNAP_CHAT_CLIENT_ID,
          redirectURI: process.env.REACT_APP_REDIRECT_URL,
          clientSecret: process.env.REACT_APP_SNAP_CHAT_CLIENT_SECRET,
          appName: "Digital Vibes",
          scopeList: [
            "user.display_name",
            "user.bitmoji.avatar",
            "user.external_id",
          ],
          handleResponseCallback: handleLoginResponse,
        });
      } else {
        console.error("Snap Kit SDK not available.");
      }
    };

    const handleLoginResponse = () => {
      window.snap.loginkit.fetchUserInfo().then(
        (result) => {
          const userData = result.data.me;
          setDisplayName(userData.displayName);
          setBitmojiSrc(userData.bitmoji.avatar);
          setExternalId(userData.externalId);
        },
        (err) => {
          console.log(err); // Error
        },
      );
    };

    // Load the SDK asynchronously
    (function (d, s, id) {
      var js,
        sjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s);
      js.id = id;
      js.src = "https://sdk.snapkit.com/js/v1/login.js";
      sjs.parentNode.insertBefore(js, sjs);
    })(document, "script", "loginkit-sdk");

    // Call Snap Kit initialization function when component mounts
    snapKitInit();

    // Clean up function
    return () => {
      // Perform any cleanup if needed
    };
  }, []);

  return (
    <div>
      <div id="display_name">{displayName}</div>
      <img id="bitmoji" alt="Bitmoji" src={bitmojiSrc} />
      <div id="external_id">{externalId}</div>
      <hr />
      <div id="my-login-button-target"></div>
    </div>
  );
};

export default SnapKitLoginButton;
