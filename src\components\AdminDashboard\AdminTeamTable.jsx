import { useMemo, useState, useEffect } from "react";
import {
  useAsyncDebounce,
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import FetchingDataAdmin from "../LoadingAnimation/FetchingDataAdmin";
import {
  Button,
  Col,
  Form,
  InputGroup,
  Pagination,
  Row,
} from "react-bootstrap";
import { BsFillCaretDownFill } from "react-icons/bs";
import { BiSearch } from "react-icons/bi";
import "../CustomDataTable/datatable.css";
import { setRoles } from "../../redux/features/roleSlice";
import { useDispatch, useSelector } from "react-redux";
import getAllRolesApi from "../../services/roles/get-all-roles.api";
function GlobalFilter({
  preGlobalFilteredRows,
  globalFilter,
  value,
  setValue,
  setGlobalFilter,
}) {
  const count = preGlobalFilteredRows.length;
  useEffect(() => {
    if (globalFilter === "") {
      setValue("");
    }
  }, [globalFilter]);
  const onChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 200);

  return (
    <div className={"position-relative"}>
      <InputGroup>
        <Form.Control
          aria-label="Default"
          aria-describedby="inputGroup-sizing-default"
          value={value || ""}
          onChange={(e) => {
            setValue(e.target.value);
            onChange(e.target.value);
          }}
          placeholder={`${count} records...`}
          className={"search-input"}
        />
      </InputGroup>
      <div className={"search-icon"}>
        <BiSearch color={"#000"} size={20} />
      </div>
    </div>
  );
}

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={`Search ${count} records...`}
    />
  );
}

const AdminTeamTable = ({ data, columns, loading, setShowCenteredModal }) => {
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  useEffect(() => {
    const fetchRoles = async () => {
      const roleType = user.user.flag === "admin" ? "admin" : "user";
      const response = await getAllRolesApi(roleType);
      dispatch(setRoles(response?.data));
    };
    fetchRoles();
  }, [dispatch, user]);
  const [value, setValue] = useState("");
  const defaultColumn = useMemo(
    () => ({
      // Default Filter UI
      Filter: DefaultColumnFilter,
    }),
    []
  );
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    preGlobalFilteredRows,
    setGlobalFilter,
    setFilter,
    page,
    canPreviousPage,
    canNextPage,
    pageOptions,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    state: { pageIndex, pageSize, globalFilter },
  } = useTable(
    {
      columns,
      data,
      defaultColumn,
      initialState: { pageIndex: 0, pageSize: 5 },
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const handleClearFilters = () => {
    setGlobalFilter("");
    setValue("");
    columns.forEach((column) => {
      if (column.accessor) {
        setFilter(column.accessor, undefined);
      }
    });
  };
  return (
    <>
      {loading ? (
        <FetchingDataAdmin className={"admin-theme"} />
      ) : (
        <div className={"all-leads-table admin-theme"}>
          <Row
            className={"mx-2 my-3 justify-content-between align-items-center"}
          >
            <Col lg={5}>
              <GlobalFilter
                preGlobalFilteredRows={preGlobalFilteredRows}
                globalFilter={globalFilter}
                setGlobalFilter={setGlobalFilter}
                value={value}
                setValue={setValue}
              />
            </Col>

            <Col className={"text-end"} lg={7}>
              <Button className={"new-client-btn"} onClick={handleClearFilters}>
                Clear Filters
              </Button>
              <Button
                className={"new-client-btn ms-3"}
                onClick={() => setShowCenteredModal(true)}
              >
                Invite New Member
              </Button>
            </Col>
          </Row>
          {data.length > 0 ? (
            <>
              <table
                className="table text-center table-responsive-md"
                {...getTableProps()}
              >
                <thead>
                  {headerGroups?.map((headerGroup, index) => (
                    <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                      {headerGroup.headers.map((column, i) => (
                        <th
                          {...column.getHeaderProps(
                            column.getSortByToggleProps()
                          )}
                          key={i}
                        >
                          {column.render("Header")}
                          {/* Render the columns filter UI */}
                          {/*<div>{column.canFilter ? column.render('Filter') : null}</div>*/}
                          <span>
                            {column.isSorted ? (
                              column.isSortedDesc ? (
                                " 🔽"
                              ) : (
                                " 🔼"
                              )
                            ) : (
                              <> {column.accessor && <BsFillCaretDownFill />}</>
                            )}
                          </span>
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody {...getTableBodyProps()}>
                  {page?.map((row) => {
                    prepareRow(row);
                    return (
                      <tr
                        {...row.getRowProps()}
                        className={"client-table-row filter-table-rows"}
                        key={row.original.id}
                      >
                        {row?.cells?.map((cell, i) => {
                          return (
                            <td {...cell.getCellProps()} key={i}>
                              {cell.render("Cell")}
                            </td>
                          );
                        })}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
              <div
                className={
                  "d-flex justify-content-between flex-column flex-md-row align-items-center my-4 mx-3 text-white"
                }
              >
                <div
                  className={
                    "d-flex flex-column justify-content-center align-items-center"
                  }
                >
                  <div className={"mb-1"}>Records Per Page</div>
                  <div
                    className="btn-group records-buttons-container"
                    role="group"
                  >
                    {[5, 10, 20, 30, 40, 50].map((pageSizeOption) => (
                      <div
                        key={pageSizeOption}
                        role="button"
                        className={`${
                          pageSize === pageSizeOption
                            ? "record-button-selected"
                            : "record-button"
                        }`}
                        onClick={() => setPageSize(pageSizeOption)}
                      >
                        {pageSizeOption}
                      </div>
                    ))}
                  </div>
                </div>
                <Pagination className={"data-table-pagination"}>
                  <Pagination.Prev
                    onClick={() => previousPage()}
                    disabled={!canPreviousPage}
                  />
                  {Array.from({ length: pageOptions.length }).map(
                    (_, index) => {
                      if (
                        pageOptions.length <= 5 ||
                        index === 0 ||
                        index === pageOptions.length - 1 ||
                        (index >= pageIndex - 2 && index <= pageIndex + 2)
                      ) {
                        return (
                          <Pagination.Item
                            key={index}
                            onClick={() => gotoPage(index)}
                            active={pageIndex === index}
                          >
                            {index + 1}
                          </Pagination.Item>
                        );
                      } else if (
                        index === 1 ||
                        index === pageOptions.length - 2
                      ) {
                        return <Pagination.Ellipsis key={index} />;
                      }
                      return null;
                    }
                  )}
                  <Pagination.Next
                    onClick={() => nextPage()}
                    disabled={!canNextPage}
                  />
                </Pagination>
                <div
                  className={
                    "d-flex justify-content-between align-items-center"
                  }
                >
                  <div className="me-2 d-flex justify-content-start">
                    <div>Page </div>
                    <strong className={"d-flex justify-content-start"}>
                      <div>
                        <input
                          className="page-number-input"
                          type="number"
                          defaultValue={pageIndex + 1}
                          onChange={(e) => {
                            const page = e.target.value
                              ? Number(e.target.value) - 1
                              : 0;
                            gotoPage(page);
                          }}
                        />
                      </div>
                      <div className={"me-2"}>of {pageOptions.length}</div>
                    </strong>
                  </div>
                </div>
              </div>
            </>
          ) : (
            "No Team Members Found"
          )}
        </div>
      )}
    </>
  );
};

export default AdminTeamTable;
