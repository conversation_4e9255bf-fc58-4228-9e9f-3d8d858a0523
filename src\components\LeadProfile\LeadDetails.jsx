import Form from "react-bootstrap/Form";

const LeadDetails = ({leadDetails}) => {
    // switch case for leadDetails.status
    const statusName =  (status) => {
        switch (status) {
            case 0:
                return "Pending";
            case 1:
                return "In Progress";
            case 2:
                return "Completed";
            case 3:
                return "Rejected";
            case 4:
                return "Wrong Lead";
            case 5:
                return "Not Qualified";
            case 6:
                return "No Communication";
            case 7:
                return "Booked";
            case 8:
                return "Booked and Reserved";
            case 9:
                return "Canceled";
            case 10:
                return "Quotation"
            default:
                return "Unknown";
        }
    }
    return (
        <>
            <Form.Group className="mb-3">
                <Form.Label>Name</Form.Label>
                <Form.Control
                    name="name"
                    type="text"
                    placeholder="Name"
                    value={leadDetails?.name}
                    disabled
                />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Email Address</Form.Label>
                <Form.Control
                    name="email"
                    type="email"
                    placeholder="Email Address"
                    value={leadDetails?.email}
                    disabled
                />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Phone Number</Form.Label>
                <Form.Control
                    name="phone"
                    type="text"
                    placeholder="Phone Number"
                    value={leadDetails?.phone}
                    disabled
                />
            </Form.Group>
            <Form.Group className="mb-4">
                <Form.Label>Status</Form.Label>
                <Form.Select
                    name="status"
                    aria-label="Lead Status"
                    value={leadDetails?.status || ""}
                    disabled
                >
                    <option value="">{statusName(leadDetails?.status)}</option>
                </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Service</Form.Label>
                <Form.Control
                    name="service"
                    type="text"
                    placeholder="Service"
                    value={leadDetails?.service}
                    disabled
                />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Amount</Form.Label>
                <Form.Control
                    name="amount"
                    type="number"
                    placeholder="Amount"
                    value={leadDetails?.amount}
                    disabled
                />
            </Form.Group>
        </>
    );
};

export default LeadDetails;