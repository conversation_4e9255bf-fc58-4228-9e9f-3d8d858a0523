name: Deploy to Production Environment

on:
  push:
    branches:
      - main

env:
  CI: false

jobs:
  deploy:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.12.2]

    steps:
      - name: Checkout Code 🚚
        uses: actions/checkout@v3

      - name: Setup pnpm 📦
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Setup Node.js ${{ matrix.node-version }} 🔧
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install Dependencies 📦
        run: pnpm install

      - name: Set Environment Variables for Test Environment 🌐
        run: cp .env.production .env.local

      - name: Build React App 🏗️
        run: |
          NODE_OPTIONS="--max_old_space_size=4096" pnpm run build

      - name: Upload to cPanel 📂
        uses: SamKirkland/FTP-Deploy-Action@v4.3.4
        with:
          server: 194.163.140.1
          username: webdvconnect
          password: HlvXQfA4mpIVgr1
          local-dir: ./build/
          server-dir: /public_html/
          exclude: |
            **/.git**
            **/.git*/**
            **/node_modules/**
            **/.**