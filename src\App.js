import {
  lazy, Suspense
  , useEffect
} from "react";
// import { ErrorBoundary } from "react-error-boundary";
import { Route, Routes } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { useSelector, useDispatch } from 'react-redux';
import { setShowExpiredSessionModal } from './redux/features/authSlice';
import Layout from "./components/Layout/Layout";
import LoadingAnimation from "./components/LoadingAnimation/LoadingAnimation";
import Protected from "./components/ProtectedRoute/ProtectedRouteClient.component";
import ProtectedRouteAdmin from "./components/ProtectedRoute/ProtectedRouteAdmin.component";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap/dist/css/bootstrap.rtl.min.css";
import "react-toastify/dist/ReactToastify.css";
import 'react-loading-skeleton/dist/skeleton.css';
import "./App.css";
import './utils/i18n';
import "../src/styles/dropzone.css";
import useSecurityProtection from "./hooks/useSecurityProtection";
import ErrorBoundary from "./ErrorBoundary";
import { setupGlobalAdminChatListener } from "./redux/features/adminSupportChatSlice.js";
import { setupGlobalChatListener } from "./redux/features/supportChatSlice.js";
// import CheckoutForm from "./pages/Stripe/CheckoutForm";
// import Return from "./pages/Stripe/Return";

// Lazy loaded components
const HomePage = lazy(() => import("./pages/Home/Home.page"));
const LeadsProfilePage = lazy(
  () => import("./pages/LeadsProfile/LeadsProfile.page"),
);
const SignInSignUpClient = lazy(
  () => import("./pages/SignInSignUpClient/SignInSignUpClient.page"),
);
const ForgotPasswordPage = lazy(
  () => import("./pages/ForgotPassword/ForgotPassword.page"),
);
const ResendEmailPage = lazy(
  () => import("./pages/ResetPassword/ResendEmailPage"),
);
const SetNewPasswordPage = lazy(
  () => import("./pages/SetNewPassword/SetNewPassword.page"),
);
const IntegrationsPage = lazy(
  () => import("./pages/Integrations/Integrations.page"),
);
const ClientsPage = lazy(() => import("./pages/clients/Clients.page"));
const NoCommunicationLeadsPage = lazy(() => import("./pages/NoCommunicationLeads/NoCommunicationLeads.page"));
const AccountSettingsPage = lazy(
  () => import("./pages/AccountSettings/AccountSettings.page"),
);
const ContentPage = lazy(() => import("./pages/Content/Content.page"));
const TeamPage = lazy(() => import("./pages/Team/Team.page"));
const HelpCenterPage = lazy(() => import("./pages/HelpCenter/HelpCenter.page"));
const SingleIntegrationPage = lazy(
  () => import("./pages/Integrations/SingleIntegration/SingleIntegration.page"),
);
const IntegratedPlatformPage = lazy(
  () =>
    import("./pages/Integrations/IntegratedPlatform/IntegratedPlatform.page"),
);
const TeamMemberProfilePage = lazy(
  () => import("./pages/Team/TeamMemberProfile.page"),
);
const ReportsPage = lazy(() => import("./pages/Reports/Reports.page"));
const RolesPermissionsPage = lazy(
  () => import("./pages/RolesPermissions/RolesPermissions.page"),
);
const NotFoundPage = lazy(() => import("./pages/NotFound/NotFound.page"));
const UnAuthorizedPage = lazy(
  () => import("./pages/Unauthorized/UnAuthorized.page"),
);
const TermsPage = lazy(() => import("./pages/Terms/Terms.page"));
const AdminLoginPage = lazy(() => import("./pages/AdminAuth/AdminLogin.page"));
const FormsComponent = lazy(
  () => import("./components/IntegratedPlatformPages/Forms.component"),
);
const AdminDashboard = lazy(
  () => import("./pages/AdminDashboard/AdminDashboard.page"),
);
const LayoutComponent = lazy(
  () => import("./components/AdminDashboard/Layout.component"),
);
const Clients = lazy(() => import("./pages/AdminDashboard/Clients"));
const Team = lazy(() => import("./pages/AdminDashboard/Team"));
// const Reports = lazy(() => import("./pages/AdminDashboard/Reports"));
const Settings = lazy(() => import("./pages/AdminDashboard/Settings"));
const Setup = lazy(() => import("./pages/AdminDashboard/Setup"));
const Ticket = lazy(() => import("./pages/AdminDashboard/Ticket"));
const Packages = lazy(() => import("./pages/AdminDashboard/Packages"));
const ClientPages = lazy(() => import("./pages/Subscription/SubscriptionPage"));
const PackagePurchasePage = lazy(() => import("./pages/Subscription/PackagePurchase.page"));
const SuccessPage = lazy(
  () => import("./pages/Subscription/SuccessPayment.page"),
);
const FailedPage = lazy(
  () => import("./pages/Subscription/FailedPayment.page"),
);
const LeadsForClient = lazy(
  () => import("./pages/AdminDashboard/LeadsForClient"),
);
const LeadProfile = lazy(
  () => import("./components/AdminDashboard/LeadProfile"),
);
const TeamMemberAdminProfile = lazy(
  () => import("./components/AdminDashboard/TeamMemberAdminProfile"),
);
const Logs = lazy(() => import("./pages/Logs/Logs"));
const BusinessSuiteChatPage = lazy(
  () => import("./pages/BusinessSuiteChat/BusinessSuiteChat.page"),
);
const SnapKitLoginButton = lazy(
  () => import("./components/SnapChat/SnapKitLoginButton"),
);
const RedirectPage = lazy(() => import("./pages/Redirect/RedirectPage"));
const PrivacyPolicyPage = lazy(
  () => import("./pages/PrivacyPolicy/PrivacyPolicy.page"),
);
const IntegratedTiktok = lazy(
  () => import("./pages/Integrations/IntegratedPlatform/IntegratedTiktok"),
);
const ClearBackEndCache = lazy(() => import("./pages/Logs/ClearBackEndCache"));
const TikTokRedirect = lazy(() => import("./pages/TikTok/TikTokRedirect"));
const Marketing = lazy(() => import("./pages/Marketing/MarketingDetails"));
const InvoicesAndSubs = lazy(() => import("./pages/Subscription/SubscriptionsInvoicesDetails.page.jsx"));
const SubscriptionManagment = lazy(() => import("./pages/AdminDashboard/Subscriptions/SubscriptionsManagement.jsx"));
const SubscriptionDetails = lazy(() => import("./pages/AdminDashboard/Subscriptions/SubscriptionDetails.jsx"));
const SupportPage = lazy(() => import("./pages/Support/Support.page")); // Added SupportPage
const AdminSupport = lazy(() => import("./pages/AdminDashboard/AdminSupport"));
const AdminSupportChatPage = lazy(() => import("./pages/AdminDashboard/AdminSupportChat"));
function App() {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.auth.user);
  const userFlag = useSelector((state) => state.auth.userFlag);

  // Set up global chat listeners when the app starts
  useEffect(() => {
    let unsubscribeFunction = null;

    if (user?.user?.id) {
      if (userFlag === 'admin') {
        // For admin users, set up listener for all client chats
        dispatch(setupGlobalAdminChatListener())
          .then((result) => {
            if (result.payload) {
              unsubscribeFunction = result.payload;
            }
          });
      } else {
        // For regular users, set up listener for their own chat
        dispatch(setupGlobalChatListener(user.user.id))
          .then((result) => {
            if (result.payload) {
              unsubscribeFunction = result.payload;
            }
          });
      }
    }

    // Clean up the listener when component unmounts
    return () => {
      if (typeof unsubscribeFunction === 'function') {
        unsubscribeFunction();
      }
    };
  }, [dispatch, user?.user?.id, userFlag]);
  // useSecurityProtection();

  return (
    <>
      <ToastContainer />
      <ErrorBoundary setShowExpiredSessionModal={(value) => dispatch(setShowExpiredSessionModal(value))}>
        <Suspense fallback={<LoadingAnimation />}>
          <Routes>
            <Route path={"/"} element={<Layout />}>
              <Route
                path="/packages"
                element={
                  <Protected
                    isSignedIn={user}
                  // restrictTeamMember={true}
                  >
                    <ClientPages />
                  </Protected>
                }
              />
              <Route
                path="/invoices-and-subscriptions"
                element={
                  <Protected
                    isSignedIn={user}
                  // restrictTeamMember={true}
                  >
                    <InvoicesAndSubs />
                  </Protected>
                }
              />
              <Route
                path="/payment/success"
                element={
                  <Protected
                    isSignedIn={user}
                  >
                    <SuccessPage />
                  </Protected>
                }
              />
              <Route
                path="/payment/failed"
                element={
                  <Protected
                    isSignedIn={user}
                  >
                    <FailedPage />
                  </Protected>
                }
              />
              <Route
                path="/subscription"
                element={
                  <ClientPages />
                }
              />
              <Route
                path="/package-purchase"
                element={
                  <PackagePurchasePage />
                }
              />
              <Route index element={
                <Protected isSignedIn={user}>
                  <HomePage />
                </Protected>
              }
              />
              <Route
                path="/leads/:id"
                element={
                  <Protected isSignedIn={user}>
                    <LeadsProfilePage />
                  </Protected>
                }
              />
              <Route
                path="/leads"
                element={
                  <Protected isSignedIn={user}>
                    <ClientsPage />
                  </Protected>
                }
              />
              <Route
                path="/no-communication-leads"
                element={
                  <Protected isSignedIn={user}>
                    <NoCommunicationLeadsPage />
                  </Protected>
                }
              />
              <Route
                path="/integrations"
                element={
                  <Protected isSignedIn={user}>
                    <IntegrationsPage />
                  </Protected>
                }
              />
              <Route
                path="/account-settings"
                element={
                  <Protected isSignedIn={user}>
                    <AccountSettingsPage />
                  </Protected>
                }
              />
              <Route
                path="/content"
                element={
                  <Protected isSignedIn={user}>
                    <ContentPage />
                  </Protected>
                }
              />
              <Route
                path="/team"
                element={
                  <Protected
                    isSignedIn={user}
                    requiredPermissions={["member-list"]}
                  >
                    <TeamPage />
                  </Protected>
                }
              />
              <Route
                path="/team/:id"
                element={
                  <Protected
                    isSignedIn={user}
                    requiredPermissions={["member-list"]}
                  >
                    <TeamMemberProfilePage />
                  </Protected>
                }
              />
              <Route
                path="/support"
                element={
                  <Protected isSignedIn={user}>
                    <SupportPage />
                  </Protected>
                }
              />
              <Route path="/terms" element={<TermsPage />} />
              <Route path="/privacy" element={<PrivacyPolicyPage />} />
              <Route
                path="/meta-business-suite"
                element={
                  <Protected isSignedIn={user}>
                    <BusinessSuiteChatPage />
                  </Protected>
                }
              />
              <Route
                path="/integrations/:id"
                element={
                  <Protected isSignedIn={user}>
                    <SingleIntegrationPage />
                  </Protected>
                }
              />
              <Route
                path="/integrations/facebook/connected"
                element={
                  <Protected isSignedIn={user}>
                    <IntegratedPlatformPage />
                  </Protected>
                }
              />
              <Route
                path="/integrations/tiktok/connected"
                element={
                  <Protected isSignedIn={user}>
                    <IntegratedTiktok />
                  </Protected>
                }
              />
              <Route
                path="/integrations/:id/forms"
                element={
                  <Protected isSignedIn={user}>
                    <FormsComponent />
                  </Protected>
                }
              />
              <Route
                path="/reports"
                element={
                  <Protected isSignedIn={user}
                  // restrictTeamMember={true}
                  >
                    <ReportsPage />
                  </Protected>
                }
              />
              <Route path="/auth/tiktok" element={<TikTokRedirect />} />
              <Route
                path="/permissions"
                element={
                  <Protected
                    isSignedIn={user}
                    requiredPermissions={["role-list"]}
                  // restrictTeamMember={true}
                  >
                    <RolesPermissionsPage />
                  </Protected>
                }
              />
              {/*<Route*/}
              {/*  path="/subscription"*/}
              {/*  element={*/}
              {/*    <Protected*/}
              {/*      isSignedIn={user}*/}
              {/*      requiredPermissions={["subscription-list"]}*/}
              {/*    >*/}
              {/*      <SubscriptionPage />*/}
              {/*    </Protected>*/}
              {/*  }*/}
              {/*/>*/}

              <Route
                path="/test-snap"
                element={
                  <Protected isSignedIn={user}>
                    <div>
                      <SnapKitLoginButton />
                    </div>
                  </Protected>
                }
              />
              <Route
                path="/marketing"
                element={
                  <Protected
                    isSignedIn={user}
                  // restrictTeamMember={true}
                  >
                    <Marketing />
                  </Protected>
                }
              />
            </Route>
            {/*<Route*/}
            {/*    path="/checkout"*/}
            {/*    element={*/}
            {/*        <Protected*/}
            {/*            isSignedIn={user}*/}
            {/*        >*/}
            {/*            <CheckoutForm />*/}
            {/*        </Protected>*/}
            {/*    }*/}
            {/*/>*/}
            {/*<Route*/}
            {/*    path="/checkout"*/}
            {/*    element={*/}
            {/*        <Protected*/}
            {/*            isSignedIn={user}*/}
            {/*        >*/}
            {/*            <Return />*/}
            {/*        </Protected>*/}
            {/*    }*/}
            {/*/>*/}
            <Route path="/client/login" element={<SignInSignUpClient />} />
            <Route path="/tiktok_redir" element={<RedirectPage />}></Route>
            <Route path="/test" element={<RedirectPage />}></Route>
            <Route
              path="/client/forgot-password"
              element={<ForgotPasswordPage />}
            />
            <Route
              path="/client/reset-password"
              element={<ResendEmailPage />}
            />
            <Route
              path="/client/set-new-password"
              element={<SetNewPasswordPage />}
            />
            <Route path="/logs" element={<Logs />} />

            <Route
              path="/admin"
              element={
                <ProtectedRouteAdmin
                  isSignedIn={user}
                  requiredRoles={["Owner"]}
                >
                  <LayoutComponent />
                </ProtectedRouteAdmin>
              }
            >
              <Route
                path="/admin/dashboard"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <AdminDashboard />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/clients"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <Clients />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/team"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <Team />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/packages"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <Packages />
                  </ProtectedRouteAdmin>
                }
              />
              {/*<Route*/}
              {/*  path="/admin/subscription"*/}
              {/*  element={*/}
              {/*    <ProtectedRouteAdmin*/}
              {/*      isSignedIn={user}*/}
              {/*      requiredRoles={["Owner"]}*/}
              {/*    >*/}
              {/*      <Subscription />*/}
              {/*    </ProtectedRouteAdmin>*/}
              {/*  }*/}
              {/*/>*/}
              {/* <Route
                path="/admin/reports"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <Reports />
                  </ProtectedRouteAdmin>
                }
              /> */}
              <Route
                path="/admin/settings"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <Settings />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/setup"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <Setup />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/ticket"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <Ticket />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/clients/leads/:id"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <LeadsForClient />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/clients/leads/profile/:id"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <LeadProfile />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/team-member/profile/:id"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <TeamMemberAdminProfile />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/subscriptions/management"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <SubscriptionManagment />
                  </ProtectedRouteAdmin>
                }
              />
              <Route path="/admin/subscriptions/management/details" element={<SubscriptionDetails />} />
              <Route
                path="/admin/support"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                    requiredRoles={["Owner"]}
                  >
                    <AdminSupport />
                  </ProtectedRouteAdmin>
                }
              />
              <Route
                path="/admin/support/chat"
                element={
                  <ProtectedRouteAdmin
                    isSignedIn={user}
                  >
                    <AdminSupportChatPage />
                  </ProtectedRouteAdmin>
                }
              />
            </Route>
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route path="*" element={<NotFoundPage />} />
            <Route path={"/unauthorized"} element={<UnAuthorizedPage />} />
            <Route path="/clear" element={<ClearBackEndCache />} />
          </Routes>
        </Suspense>
      </ErrorBoundary>
    </>
  );
}

export default App;
