import apiRequest from "../../utils/apiRequest";

const addClientApi = (clientData) => {
    return apiRequest("admin/client", "post", clientData);
};

const deleteClientApi = ({ clientId }) => {
    return apiRequest(`admin/client/${clientId}`, "delete");
};

const editClientApi = async (clientId, clientData) => {
    // Pass success message directly to apiRequest
    return apiRequest(
        `admin/client/${clientId}`,
        "put",
        clientData,
        {},
        null,
        {},
        "Client updated successfully", // Custom success message
        "Failed to update client" // Custom error message
    );
};

const exportClientsApi = async () => {
    try {
        const response = await apiRequest("admin/clients/export", "get", null, {
            responseType: "blob", // Set the response type to 'blob' to handle binary data
            headers: {
                'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            },
        });

        // Create a Blob object from the response data
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        // Generate a URL for the Blob object
        const url = window.URL.createObjectURL(blob);

        // Create a link element to trigger the download
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'clients.xlsx'); // Set the filename for download
        document.body.appendChild(link);

        // Trigger the download
        link.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        // Return something or handle completion if needed, error is handled by apiRequest
    } catch (error) {
        // Error toast is now handled by apiRequest, but we might need specific logic here
        // For now, re-throw the error to be caught by the caller or apiRequest's handler
        console.error('Error during blob processing or download:', error);
        // If apiRequest didn't show a toast (e.g., network error before response), we might need one here.
        // However, apiRequest should cover most cases.
        throw error;
    }
};

const getAllClientsApi = async (currentPage, recordsPerPage) => {
    const url = currentPage && recordsPerPage
        ? `admin/client?per_page=${recordsPerPage}&current_page=${currentPage}`
        : "admin/client";

    return await apiRequest(url, "get");
};

const showClientDetails = async (clientId) => {
    const url = `admin/client/${clientId}`;
    return await apiRequest(url, "get");
};

export default {
    addClientApi,
    deleteClientApi,
    editClientApi,
    exportClientsApi,
    getAllClientsApi,
    showClientDetails
}
