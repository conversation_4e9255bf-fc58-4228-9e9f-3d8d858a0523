const FetchingDataAdmin = ({className}) => {
    return (
            <div className={`animation-example ${className}`} style={{width: "50vmin"}}>
                <div className='item'>
                    <div className='line'></div>
                    <div className='dot'></div>
                    <div className='circle'></div>
                </div>
                <div className='item'>
                    <div className='line'></div>
                    <div className='dot'></div>
                    <div className='circle'></div>
                </div>
                <div className='item'>
                    <div className='line'></div>
                    <div className='dot'></div>
                    <div className='circle'></div>
                </div>
                <div className='item'>
                    <div className='line'></div>
                    <div className='dot'></div>
                    <div className='circle'></div>
                </div>
                <div className='item -type2'>
                    <div className='line'></div>
                    <div className='dot'></div>
                    <div className='circle'></div>
                </div>
                <div className='item -type2'>
                    <div className='line'></div>
                    <div className='dot'></div>
                    <div className='circle'></div>
                </div>
                <div className='item -type2'>
                    <div className='line'></div>
                    <div className='dot'></div>
                    <div className='circle'></div>
                </div>
                <div className='item -type2'>
                    <div className='line'></div>
                    <div className='dot'></div>
                    <div className='circle'></div>
                </div>
                <div className='center'>
                    <div className='circle'></div>
                    <div className='circle'></div>
                    <div className='circle'></div>
                </div>
            </div>
    );
};

export default FetchingDataAdmin;