.policy-header {
    border-radius: 14px;
    background: linear-gradient(264deg, #92C020 2.52%, #CAD511 84.7%);
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    color: #FFFFFF;
    text-align: center;
    padding: 50px;
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 30px;
}

.policy-navs {
    border-radius: 18px;
    background: #FFF;
    box-shadow: 0 4px 38px -7px rgba(0, 0, 0, 0.15);
    padding: 1.2rem 1rem;
}

.policy-navs.nav-pills .nav-link {
    color: #000000;
    font-size: 1rem;
    font-weight: 400;
}

.policy-navs.nav-pills .nav-link.active{
    color: #92C020;
    font-weight: 900;
    background-color: transparent;
    transition: ease-in-out all 0.5s;
}

.policy-navs .nav-link {
    color: #333;
    border-radius: 0;
    border-left: 3px solid transparent;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.policy-navs .nav-link:hover {
    background-color: #f5f5f5;
    border-left: 3px solid #ddd;
}

.policy-navs .nav-link.active {
    background-color: #f0f8ff;
    color: #007bff;
    border-left: 3px solid #007bff;
    font-weight: 600;
}

.privacy-content {
    line-height: 1.6;
}

.privacy-content p {
    margin-bottom: 0.5rem;
}

/* RTL support for Arabic */
[dir="rtl"] .policy-navs .nav-link {
    border-left: none;
    border-right: 3px solid transparent;
}

[dir="rtl"] .policy-navs .nav-link:hover {
    border-left: none;
    border-right: 3px solid #ddd;
}

[dir="rtl"] .policy-navs .nav-link.active {
    border-left: none;
    border-right: 3px solid #007bff;
}

/* Mobile styles */
@media (max-width: 768px) {
    .policy-header {
        font-size: 1.75rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    h3 {
        font-size: 1.25rem;
    }
}
