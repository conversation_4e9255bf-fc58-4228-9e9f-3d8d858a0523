/**
 * BigIntUtil is a custom utility that mimics some behavior of the native BigInt.
 * It stores the big integer as a string internally.
 *
 * Note:
 * - If a number is provided and it is not safe (i.e. > Number.MAX_SAFE_INTEGER),
 *   precision may already be lost. It’s best to provide a string for full precision.
 */
export class BigIntUtil {
    constructor(value) {
        if (typeof value === "string") {
            // Validate that the string is composed of only digits (optionally with a leading '+' sign)
            if (!/^\+?\d+$/.test(value)) {
                throw new Error("Invalid string for BigIntUtil. Expecting only digits.");
            }
            // Remove any leading '+' or zeros (if you wish to keep zeros, adjust accordingly)
            this.value = value.replace(/^\+?0+(?!$)/, "");
        } else if (typeof value === "number") {
            // When a number is passed, warn if it is unsafe.
            if (!Number.isSafeInteger(value)) {
                console.warn(
                    "Warning: The number passed is not a safe integer. Precision may have been lost. " +
                    "It is recommended to store big integers as strings."
                );
            }
            this.value = value.toString();
        } else if (value instanceof BigIntUtil) {
            this.value = value.value;
        } else {
            throw new Error("Unsupported type for BigIntUtil");
        }
    }

    /**
     * Returns the string representation of the big integer.
     */
    toString() {
        return this.value;
    }

    /**
     * Example: Adds another BigIntUtil (or a number/string that represents a non-negative integer)
     * and returns a new BigIntUtil instance with the result.
     *
     * This implements digit-by-digit addition. It assumes non-negative numbers.
     *
     * @param {BigIntUtil|string|number} other
     * @returns {BigIntUtil}
     */
    add(other) {
        const a = this.value;
        const b = new BigIntUtil(other).value;
        let result = "";
        let carry = 0;
        let i = a.length - 1;
        let j = b.length - 1;

        while (i >= 0 || j >= 0 || carry) {
            const digitA = i >= 0 ? parseInt(a[i], 10) : 0;
            const digitB = j >= 0 ? parseInt(b[j], 10) : 0;
            const sum = digitA + digitB + carry;
            carry = Math.floor(sum / 10);
            result = (sum % 10).toString() + result;
            i--;
            j--;
        }

        return new BigIntUtil(result);
    }

    /**
     * (Optional) You can add more arithmetic or utility methods as needed.
     */
}
