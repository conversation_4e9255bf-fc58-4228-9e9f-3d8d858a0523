import {<PERSON>} from "react-router-dom";
import {But<PERSON>} from "react-bootstrap";
import React, {useState} from "react";
import leadService from "../../services/leads";
import { useSelector } from "react-redux";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import ConfirmModal from "../Modals/ConfirmModal";


const LeadAssignmentComponent = () => {
    const [selectedCard, setSelectedCard] = useState(null);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const { currentUserPermissions } = useSelector((state) => state.auth);
    const handleCardClick = (index) => {
        setSelectedCard(index === selectedCard ? null : index);
    };

    const handleAutoAssign = async () => {
        await leadService.autoAssignLeadsApi();
    }
    return (<>
        <h2>Leads Assignment</h2>
        <div className={"content-container vh-100"}>
            <div className={"overview-leadAssignment my-5 py-4"}>
                <p className={"gray-label"}>
                    When a new lead is received, you can automatically assign them to your team members or leave
                    them as
                    unassigned. This applies to new leads received across all your lead sources, such as your
                    website,
                    Facebook Lead Ads, Google Ad Lead Forms, and other integrations such as Zapier.
                </p>
                <Button as={Link} to={"/integrations"} className={"submit-btn border-0 d-flex mx-auto"}
                        style={{width: "fit-content"}}>
                    View your Lead Source Integrations
                </Button>
            </div>
            {currentUserPermissions?.includes("member-edit") ?
                <div className={`assignment-rule-card px-4 py-4 ${selectedCard === 2 ? "selected-card" : ""}`}
                     onClick={() => handleCardClick(2)}>
                    <div
                        // onClick={() => setOpen(!open)}
                        onClick={()=>setShowConfirmModal(true)}
                    >
                        <p className={"fs-5 rule-title"}>
                            Automatically Assign to team member(s)
                        </p>
                        <p className={"fs-6"}>
                            Assign old leads that are unassigned to team members randomly
                        </p>
                        <center>
                            <Button className={"submit-btn"}>
                                Automatic Assign
                            </Button>
                        </center>
                    </div>
                    <CenteredModal show={showConfirmModal}
                                   children={<ConfirmModal
                                       title={"Are you sure you want to auto assign leads to team members?"}
                                       handleClose={() => setShowConfirmModal(false)}
                                       confirmFunction={() => handleAutoAssign().then(setShowConfirmModal(false))}
                                   />}
                                   onHide={() => setShowConfirmModal(false)}
                    />
                </div> : null}
        </div>
    </>);
};

export default LeadAssignmentComponent;
