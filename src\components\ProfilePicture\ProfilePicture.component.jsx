import { FaUserCircle, FaEdit } from "react-icons/fa";
import "../../pages/AccountSettings/AccountSettings.css";
import { Dropdown, Form } from "react-bootstrap";
import { HiMiniXMark } from "react-icons/hi2";
import { FiUpload } from "react-icons/fi";

const ProfilePictureComponent = ({ accountPicture, setAccountPicture, size }) => {
  const profilePic = accountPicture ? (
    <img
      src={
        typeof accountPicture === "string"
          ? process.env.REACT_APP_PROFILE_PIC_ENDPOINT + accountPicture
          : URL.createObjectURL(accountPicture)
      }
      alt="Profile"
      className={"rounded-circle bg-white"}
    />
  ) : (
    <FaUserCircle
      className={"rounded-circle bg-white"}
      color={"gray"}
      size={size || 120}
    />
  );

  const handleEdit = (event) => {
    const file = event.target.files[0];
    if (file instanceof Blob) {
      setAccountPicture(file);
    } else {
      console.error("Invalid file type. Please select a valid image file.");
    }
  };

  const handleDelete = () => {
    setAccountPicture(null);
  };

  return (
    <div className={"user-profile-picture position-relative"}>
      {profilePic}
      <div className="edit-icon">
        <Dropdown>
          <Dropdown.Toggle id="dropdown-basic" className={"profile-pic-edit"}>
            <FaEdit />
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <div className={"dropItem-profile-pic"}>
              <Form.Group controlId="accountPicture" role={"button"}>
                <Form.Label className={"green-label mb-0"}>
                  Upload Image <FiUpload size={20} />
                </Form.Label>
                <Form.Control
                  type="file"
                  onChange={handleEdit}
                  style={{ display: "none" }}
                />
              </Form.Group>
            </div>
            <hr />
            <div className={"dropItem-profile-pic"}>
              <div className="text-danger" onClick={handleDelete}>
                Delete <HiMiniXMark size={20} />
              </div>
            </div>
          </Dropdown.Menu>
        </Dropdown>
      </div>
    </div>
  );
};

export default ProfilePictureComponent;
