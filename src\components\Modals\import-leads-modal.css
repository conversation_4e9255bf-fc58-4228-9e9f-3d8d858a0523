.dropzone-container {
    border: 2px dashed #ccc;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: border 0.3s ease;
    background: #fff;
    min-height: 150px;
    width: 100%;
    margin-bottom: 1rem;
    align-content: center;
}

.dropzone-container:hover {
    border-color: #666;
}

.dropzone-container.error {
    border-color: #dc3545;
}

.dropzone-container.active {
    border-color: #28a745;
}

.upload-placeholder {
    color: #666;
}

.upload-placeholder p {
    margin-bottom: 0.5rem;
}

.upload-placeholder em {
    font-size: 0.9em;
    color: #888;
}

.file-preview-container {
    width: 100%;
}

.file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.file-name {
    font-size: 0.9em;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 80%;
}

.remove-file-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.remove-file-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

.preview-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100px;
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 1rem;
}

.document-preview {
    text-align: center;
}

.document-preview p {
    margin: 0.5rem 0;
    color: #333;
}

.error-message {
    color: #dc3545;
    margin-top: 0.5rem;
    font-size: 0.9em;
}
