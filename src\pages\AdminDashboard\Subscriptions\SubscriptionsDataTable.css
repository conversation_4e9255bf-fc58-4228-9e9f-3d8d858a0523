/* Search Input Styles */
.search-input-group {
  max-width: 400px;
}

.search-input {
  border-radius: 39px;
  border: 1px solid #DFDFDF;
  background: #2a2a2a;
  color: #fff;
  padding: 0.5rem 1rem;
}

.search-input:focus {
  background: #2a2a2a;
  color: #fff;
  box-shadow: none;
  border-color: #92C020;
}

.search-icon-container {
  background: transparent;
  border: none;
  position: absolute;
  right: 0;
  z-index: 4;
}

.search-icon {
  color: #92C020;
}

/* Records Per Page Buttons */
.records-buttons-container {
  display: flex;
  gap: 4px;
  padding: 4px;
  border: 1px solid #92C020;
  border-radius: 5px;
  background: #2a2a2a;
}

.record-button {
  background: transparent;
  border: none;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.record-button:hover {
  background: rgba(146, 192, 32, 0.2);
}

.record-button-selected {
  background: #92C020;
  color: #fff;
}

/* Table Styles */
.table {
  margin-bottom: 0;
}

.table thead th {
  background-color: #2a2a2a;
  border-bottom: 2px solid #444;
  color: #fff;
  font-weight: 500;
}

.table tbody td {
  border-color: #444;
  vertical-align: middle;
}

/* Pagination Styles */
.data-table-pagination {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.data-table-pagination .page-link {
  background: #2a2a2a;
  border: none;
  color: #fff;
  border-radius: 5px;
  padding: 8px 12px;
  margin: 0 2px;
}

.data-table-pagination .page-link:hover {
  background: #92C020;
  color: #fff;
}

.data-table-pagination .active > .page-link {
  background: #92C020;
  color: #fff;
}

/* Loading State */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
