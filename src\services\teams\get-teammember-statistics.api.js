import apiRequest from "../../utils/apiRequest";

export const getTeamMemberStatisticsApi = async (id, flag) => {
    const url = flag === "admin" ? `admin/memberLeadsFilter/${id}` : `memberLeadsFilter/${id}`;
    return await apiRequest(url, "post");
};

export const getTeamMemberStatisticsApi2 = async (id, flag) => {
    const url = flag === "admin" ? `admin/completedleads/${id}` : `completedleads/${id}`;
    return await apiRequest(url, "post");
};
