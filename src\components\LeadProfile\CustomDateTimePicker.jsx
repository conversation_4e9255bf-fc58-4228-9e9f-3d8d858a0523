import { useState, useEffect } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const CustomDateTimePicker = ({ handleDateChange, value, onBlur }) => {
  const [startDate, setStartDate] = useState(
    value ? new Date(value) : new Date(),
  );

  useEffect(() => {
    if (value) {
      setStartDate(new Date(value));
    }
  }, [value]);

  const ExampleCustomTimeInput = ({ value, onChange }) => (
    <input
      type="text"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder="Enter time"
      style={{
        border: "1px solid pink",
        borderRadius: "5px",
        padding: "5px",
        fontSize: "14px",
      }}
    />
  );

  return (
    <DatePicker
      selected={startDate}
      onChange={(date) => {
        setStartDate(date);
        handleDateChange(date);
      }}
      onBlur={onBlur}
      showTimeInput
      customTimeInput={<ExampleCustomTimeInput />}
      showTimeSelect
      timeFormat="HH:mm"
      timeCaption="Time"
      dateFormat="MMMM d, yyyy h:mm aa"
      className={"form-control"}
      onClickOutside={onBlur}
    />
  );
};

export default CustomDateTimePicker;
