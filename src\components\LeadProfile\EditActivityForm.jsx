import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from "react-bootstrap";
import { useEffect, useRef, useState } from "react";
import {FaImage} from "react-icons/fa6";
import { useTranslation } from "react-i18next";

const EditActivityForm = ({ handleClose, activity }) => {
    const {t} = useTranslation();
    const isImageFile = (fileName) =>
        /\.(jpg|jpeg|png|gif|bmp)$/i.test(fileName);
    const isDocumentFile = (fileName) =>
        /\.(pdf|doc|docx)$/i.test(fileName);

    const fileUrl = activity?.proven
        ? process.env.REACT_APP_PROFILE_PIC_ENDPOINT + activity.proven
        : null;

    const getFileType = (fileName) => {
        if (/\.pdf$/i.test(fileName)) {
            return "application/pdf";
        } else if (/\.docx?$/i.test(fileName)) {
            return "application/msword";
        }
        return "";
    };

    const isPreviewSupported = (fileName) => /\.(jpg|jpeg|png|gif|bmp|pdf)$/i.test(fileName);

    const [isNoteExpanded, setIsNoteExpanded] = useState(false);
    const [isNoteTruncated, setIsNoteTruncated] = useState(false);
    const [isResultExpanded, setIsResultExpanded] = useState(false);
    const [isResultTruncated, setIsResultTruncated] = useState(false);

    const [isFullScreen, setIsFullScreen] = useState(false);
    const [zoom, setZoom] = useState(1); // Initial zoom level

    const noteContainerRef = useRef(null);
    const resultContainerRef = useRef(null);

    useEffect(() => {
        const element = noteContainerRef.current;
        if (element) {
            setIsNoteTruncated(element.scrollHeight > element.clientHeight);
        }
    }, [activity?.note]);

    useEffect(() => {
        const element = resultContainerRef.current;
        if (element) {
            setIsResultTruncated(element.scrollHeight > element.clientHeight);
        }
    }, [activity?.result]);

    const handleFullScreenToggle = () => {
        setIsFullScreen(!isFullScreen);
    };

    const handleZoomIn = () => {
        setZoom((prevZoom) => Math.min(prevZoom + 0.2, 3)); // Max zoom level
    };

    const handleZoomOut = () => {
        setZoom((prevZoom) => Math.max(prevZoom - 0.2, 1)); // Min zoom level
    };

    return (
        <>
            <Modal.Header closeButton>
                <Modal.Title>{t('leadProfile.activityDetails')}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {fileUrl && isImageFile(activity?.proven) ? (
                    <>
                        <h5 className="text-center">The Proof Image (<span className={"text-primary fs-6 pointer"} onClick={handleFullScreenToggle}>Preview <FaImage/></span>)</h5>
                        <img
                            loading={"lazy"}
                            src={fileUrl}
                            alt="Proof"
                            className="img-fluid"
                        />
                    </>
                ) : fileUrl && isDocumentFile(activity?.proven) ? (
                    isPreviewSupported(activity?.proven) ? (
                        <>
                            <h5 className="text-center">Proof Document</h5>
                            <div style={{ width: "100%", height: 'auto' }}>
                                <Ratio aspectRatio="1x1">
                                    <embed
                                        type={getFileType(activity.proven)}
                                        src={fileUrl}
                                        style={{ width: "100%", height: "100%", cursor: "pointer" }}
                                        onClick={handleFullScreenToggle}
                                    />
                                </Ratio>
                            </div>
                        </>
                    ) : (
                        <div>
                            <h5 className="text-center">Proof Document</h5>
                            <p>Preview not supported for this file format.</p>
                            <Button variant="primary" href={fileUrl} download>
                                Download File
                            </Button>
                        </div>
                    )
                ) : (
                    <p>{t('leadProfile.noImage')}</p>
                )}

                <ul className="lh-lg">
                    {activity?.note && (
                        <li>
                            <div
                                ref={noteContainerRef}
                                style={{
                                    display: isNoteExpanded ? "block" : "-webkit-box",
                                    WebkitLineClamp: isNoteExpanded ? "none" : 4,
                                    WebkitBoxOrient: "vertical",
                                    overflow: isNoteExpanded ? "visible" : "hidden",
                                    textOverflow: isNoteExpanded ? "clip" : "ellipsis",
                                }}
                            >
                                <strong>Note:</strong> {activity?.note}
                            </div>
                            {isNoteTruncated && (
                                <div
                                    className="link-primary show-more"
                                    role="button"
                                    onClick={() => setIsNoteExpanded(!isNoteExpanded)}
                                >
                                    {isNoteExpanded ? "Show less" : "Show more"}
                                </div>
                            )}
                        </li>
                    )}

                    {activity?.result && (
                        <li>
                            <div
                                ref={resultContainerRef}
                                style={{
                                    display: isResultExpanded ? "block" : "-webkit-box",
                                    WebkitLineClamp: isResultExpanded ? "none" : 4,
                                    WebkitBoxOrient: "vertical",
                                    overflow: isResultExpanded ? "visible" : "hidden",
                                    textOverflow: isResultExpanded ? "clip" : "ellipsis",
                                }}
                            >
                                <strong>Result:</strong> {activity?.result}
                            </div>
                            {isResultTruncated && (
                                <div
                                    className="link-primary show-more"
                                    role="button"
                                    onClick={() => setIsResultExpanded(!isResultExpanded)}
                                >
                                    {isResultExpanded ? "Show less" : "Show more"}
                                </div>
                            )}
                        </li>
                    )}
                </ul>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={handleClose}>
                {t('common.close')}
                </Button>
            </Modal.Footer>

            {/* Full-Screen Modal for image or PDF with zoom controls */}
            <Modal
                show={isFullScreen}
                onHide={handleFullScreenToggle}
                size="xl"
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>Full-Screen View</Modal.Title>
                    {/* Zoom Controls at the Top */}
                    <div
                        className="zoom-controls"
                        style={{
                            position: "absolute",
                            top: 20, // Position at the top of the modal
                            right: "50%",
                            transform: "translateX(50%)", // Center horizontally
                            zIndex: 1000,
                        }}
                    >
                        <Button variant="outline-primary" onClick={handleZoomIn} className="me-2">
                            Zoom In
                        </Button>
                        <Button variant="outline-secondary" onClick={handleZoomOut}>
                            Zoom Out
                        </Button>
                    </div>
                </Modal.Header>
                <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
                    <div className="d-flex flex-column align-items-center">
                        {fileUrl && isImageFile(activity?.proven) ? (
                            <img
                                loading={"lazy"}
                                src={fileUrl}
                                alt="Proof"
                                className="img-fluid"
                                style={{
                                    transform: `scale(${zoom})`,
                                    transition: "transform 0.2s ease-in-out",
                                }}
                            />
                        ) : fileUrl && isDocumentFile(activity?.proven) ? (
                            <embed
                                type={getFileType(activity.proven)}
                                src={fileUrl}
                                style={{
                                    width: "100%",
                                    height: "80vh",
                                    transform: `scale(${zoom})`,
                                    transition: "transform 0.2s ease-in-out",
                                }}
                            />
                        ) : (
                            <p>No image or document selected</p>
                        )}
                    </div>
                </Modal.Body>
            </Modal>
        </>
    );
};

export default EditActivityForm;
