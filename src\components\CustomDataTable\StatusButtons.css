/* Container styles */
.status-btns.btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.status-btns.btn-group > .btn {
    float: none;
    margin: 0;
    min-width: 85px;
    max-width: fit-content;
}

/* Button base styles */
.status-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    border-radius: 39px !important;
    padding: 4px 8px !important;
    border-width: 1.5px !important;
    border-style: solid !important;
    background: transparent;
    font-size: 0.85rem !important;
    white-space: nowrap !important;
}

/* Remove Bootstrap default styles */
.status-button:focus,
.status-button:active {
    text-decoration: none !important;
    box-shadow: none !important;
}

/* Status states */
.status-button-active {
    color: white !important;
}

.status-button-default {
    color: inherit;
}

/* Hover effect handled in JS for dynamic colors */
.status-button:hover span {
    color: white !important;
}

/* Icon and text styles */
.status-button span {
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.status-button span:first-child {
    margin-right: 4px;
}

/* Icon size adjustment */
.status-button svg {
    width: 16px;
    height: 16px;
}

/* Responsive adjustments - keeping same styles for mobile */
@media (max-width: 768px) {
    .status-btns.btn-group {
        width: 100%;
        justify-content: center;
    }

    .status-btns.btn-group > .btn {
        min-width: 85px;
    }
}