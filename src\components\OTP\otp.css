.mobile-otp input, .mobile-otp select, .mobile-otp .select__control {
    border: 1px solid #1A478F;
    background: #FFF;
}

.mobile-otp label {
    color: #1A478F;
}

.mobile-otp input {
    background-color: transparent;
    padding: 7px 15px;
    margin: 8px 0;
    width: 100%;
    border-radius: 7px;
    border: 2px solid rgba(0, 0, 0, 0.20);
}

.mobile-otp input::placeholder {
    color: #444444;
    opacity: 0.5;
}

.PhoneInput {
    position: relative;
}

.PhoneInputCountry {
    position: absolute;
    top: 50%;
    left: 7%;
    transform: translate(-50%, -50%);
    z-index: 99;
    @media (max-width: 1280px) {
        left: 4%;
    }
    @media (max-width: 768px) {
        left: 5%;
    }
    @media (max-width: 480px) {
        left: 10%;
    }
}

.mobile-otp input {
    padding-left: 55px;
}

.otp-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
}

.otp-container input {
    border-radius: 12px;
    border: 2px solid #92C020;
    color: #92C020;
    height: 45px !important;
    width: 45px !important;
}