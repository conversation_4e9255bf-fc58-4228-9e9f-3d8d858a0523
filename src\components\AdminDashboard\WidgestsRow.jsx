import {Col, Row} from "react-bootstrap";
import {ReactSVG} from "react-svg";
import newLeads from "../../assets/media/Icons/magnet-leads.svg";
import arrowUp from "../../assets/media/Icons/arrow-up.svg";
import arrowDown from "../../assets/media/Icons/arrow-down.svg";
import Skeleton from 'react-loading-skeleton';
import "./widgets.css";

const WidgetsRow = ({widgetData, classes}) => {

    return (<Row className="justify-content-center justify-content-md-between">
            {widgetData?.length > 0 ? (widgetData.map((data, index) => (<Col
                        key={index}
                        lg={3}
                        md={4}
                        sm={6}
                        className={`mb-4 px-4 py-3 ${classes}`}
                        style={{height: 170, maxWidth: 295}}
                    >
                        <div className="d-flex justify-content-between align-items-center">
                            <div className="d-flex justify-content-between align-items-start flex-column">
                                <div className="fs-6">{data?.title || <Skeleton/>}</div>
                                <div className="fw-bold fs-4">{data?.value || <Skeleton/>}</div>
                            </div>
                            <ReactSVG
                                className="p-2"
                                src={newLeads}
                                style={{
                                    borderRadius: "9.97px", background: "rgba(13, 153, 255, 0.1)",
                                }}
                            />
                        </div>
                        <div className="d-flex justify-content-between align-items-center">
                            <div className="fs-6">{data?.isIncrease ? "+" : null}{data?.comparison || <Skeleton/>}</div>
                            <div
                                className={`d-flex justify-content-between align-items-center ${data?.isIncrease ? "mainColor" : "text-danger"}`}
                            >
                                <span className="me-2 fs-6">{data?.percentage.toFixed(2) + "%" || <Skeleton/>}</span>
                                {data?.isIncrease !== undefined ? (
                                    <ReactSVG src={data?.isIncrease ? arrowUp : arrowDown}/>) : (
                                    <Skeleton width={20} height={20}/>)}
                            </div>
                        </div>
                    </Col>))) : (
                <div
                    className="d-flex flex-wrap justify-content-center justify-content-md-between align-items-center w-100">
                    {[...Array(4)].map((_, idx) => (
                        <div
                            key={idx}
                            className="mb-4"
                            style={{height: 170, maxWidth: 295}}
                        >
                            <Skeleton
                                height={170}
                                width={295}
                                baseColor={`${classes.includes("admin-widgets") ? "#FFFFFF" : "#444"}`}
                                highlightColor={`${classes.includes("admin-widgets") ? "#f5f5f5" : "#666"}`}
                            />
                        </div>
                    ))}
                </div>
            )}
    </Row>);
};

export default WidgetsRow;