import React from "react";
import { useEffect } from "react";
import TikTokRedirectApi from "../../services/auth/client";

const Redirect = () => {
  useEffect(() => {
    const redirectFunction = async () => {
      const urlSearchParams = new URLSearchParams(window.location.search);
      const code = urlSearchParams.get("code");
      const response = await TikTokRedirectApi(code);
      console.log(response);
    };
  }, []);

  return <div>Redirect page</div>;
};

export default Redirect;
