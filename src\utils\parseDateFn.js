
const parseDateFn = (isoDateString) => {
  // Parse the date
  const parsedDate = new Date(isoDateString);

  // Format it (example: "March 14, 2025, 12:00 PM UTC")
  const formattedDate = parsedDate.toLocaleString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    timeZone: "UTC", // Keep it in UTC (optional)
  });

  return formattedDate;
};

export default parseDateFn;
