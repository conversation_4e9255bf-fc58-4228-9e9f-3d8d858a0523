import React, { useEffect, useMemo, useState } from "react";
import { ReactSVG } from "react-svg";
import { format } from "date-fns";
import DataTableComponent from "../../CustomDataTable/DataTable.component";
import { IoSparklesSharp } from "react-icons/io5";
import { MdAssignmentTurnedIn, MdEdit } from "react-icons/md";
import { Tooltip } from "react-tooltip";
import { PiTrashFill } from "react-icons/pi";
import { FaCalendar, FaEnvelope, FaPhone, FaUserPlus } from "react-icons/fa6";
import { Dropdown } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import CenteredModal from "../../Shared/modals/CenteredModal/CenteredModal";
import DeleteLeadFromTable from "../../Modals/DeleteLeadFromTable";
import { Link } from "react-router-dom";
import leadService from "../../../services/leads";
import getAllTeamMembers from "../../../services/teams/get-teams.api";
import {
  setTeamMembers,
  setDisableAddMember,
  handleAssignTeamMemberThunk,
} from "../../../redux/features/clientSlice";
import { useTranslation } from "react-i18next";
import {
  showErrorToast,
  showSuccessToast,
} from "../../../utils/toast-success-error";
import { sourceToIcon } from "../../../constants/sourceIcons";
import { FaUserCog } from "react-icons/fa";

function FilterTableLeadsComponent({
  abortController,
  setAbortController,
  handleFilterStatus,
  loading,
  handleFilterLeads,
  applyFilters,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [teamMembers, setLocalTeamMembers] = useState([]);
  const [leadStatusCounts, setLeadStatusCounts] = useState({});
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );
  const { filterStatus, selectedSource } = useSelector((state) => state.client);
  const leads = useSelector((state) => state.client.leads);
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState(null);
  const { currentUserPermissions } = useSelector((state) => state.auth);
  const filteredTeamMembers = teamMembers?.filter(
    (teamMember) => teamMember.status !== "deactive"
  );

  const data = useMemo(() => {
    return (Array.isArray(leads) ? leads : []).map((lead, index) => ({
      id: lead?.id,
      contactName: lead?.name,
      email: lead?.email,
      source: lead?.source,
      phone: lead?.phone,
      createdBy: lead?.created_by,
      logs: lead?.latestActivity || lead?.activities,
      assignedTo: lead?.assignedTo || lead?.assigned_to?.name,
      createdAt: lead?.date || lead?.created_at,
      updatedAt: lead?.updated_at,
      status: lead?.status,
      pageName: lead?.page_name,
      formName: lead?.form_name,
      clientId: index + 1,
      service: lead?.service,
    }));
  }, [leads]);

  const [isHoverSupported, setIsHoverSupported] = useState(true);
  const [tooltipVisible, setTooltipVisible] = useState({}); // Manage which tooltip is visible

  // Get current user
  const user = useSelector((state) => state.auth.user);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const teamsData = await getAllTeamMembers();
        const members = teamsData?.data?.members || [];
        setLocalTeamMembers(members);
        dispatch(setTeamMembers(members));
        dispatch(setDisableAddMember(teamsData?.data?.quota === 0));
      } catch (error) {
        showErrorToast(
          error.response?.data?.message || t("messages.fetchTeamError")
        );
      }
    };
    fetchTeamMembers();
  }, [dispatch, t]);

  useEffect(() => {
    // Delegate abort-controller handling to applyFilters to avoid creating multiple controllers per fetch
    applyFilters(filterStatus, selectedSource);

    // Detect hover capability (unrelated to data fetching)
    const mediaQuery = window.matchMedia("(hover: hover) and (pointer: fine)");
    setIsHoverSupported(mediaQuery.matches);

    const handleChange = () => setIsHoverSupported(mediaQuery.matches);
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [currentPage, recordsPerPage, filterStatus, selectedSource]);

  const handleTouchStart = (index) => {
    // Show the tooltip when the user touches the item
    setTooltipVisible((prevState) => ({ ...prevState, [index]: true }));

    // Hide the tooltip after 2 seconds (or any duration)
    setTimeout(() => {
      setTooltipVisible((prevState) => ({ ...prevState, [index]: false }));
    }, 2000); // 2 seconds delay
  };

  const handleDelete = (id) => {
    setSelectedLeadId(id);
    setShowCenteredModal(true);
  };

  const columns = useMemo(
    () => [
      {
        Header: t("leadsTable.columns.id"),
        accessor: "clientId",
        Cell: () => null,
      },
      // Hidden column used only for sorting
      {
        id: "updatedAt",
        accessor: "updatedAt",
        Header: "Updated At",
        // Do not render anything
        Cell: () => null,
      },
      {
        Header: t("leadsTable.columns.contactName"),
        accessor: "contactName",
        Cell: ({ row }) => {
          const name = row.original.contactName;
          const unassigned =
            row.original.assignedTo === null ||
            row.original.assignedTo === undefined;
          return (
            <>
              <div
                className={`d-flex align-items-center gap-1 lead-name${row.original.id} justify-content-center`}
                style={{ maxWidth: "200px" }}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(row.original.id)
                }
              >
                {unassigned && <IoSparklesSharp size={18} color="#92C020" />}
                <span className={"one-line"}>{name}</span>
              </div>
              <Tooltip
                anchorSelect={`.lead-name${row.original.id}`}
                content={name}
                className="bg-dark text-white"
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
              />
            </>
          );
        },
      },
      {
        Header: t("leadsTable.columns.phone"),
        accessor: "phone",
      },
      {
        Header: t("leadsTable.columns.assignedTo"),
        accessor: "assignedTo",
        Cell: ({ row }) => {
          const assignedTo = row.original.assignedTo;

          return assignedTo ? (
            assignedTo
          ) : (
            <>
              <Dropdown>
                <Dropdown.Toggle
                  variant="light"
                  id="dropdown-basic"
                  className={"team-actions-button p-0 rounded-3"}
                >
                  <div className={"assign-client-icon m-0"}>
                    <FaUserPlus size={20} />
                  </div>
                </Dropdown.Toggle>
                <Dropdown.Menu className={"team-actions-menu"} container="body">
                  {filteredTeamMembers?.length > 0 ? (
                    filteredTeamMembers?.map((member) => (
                      <Dropdown.Item
                        key={member.id}
                        className={
                          "d-flex justify-content-between align-items-center text-secondary"
                        }
                        onClick={() =>
                          dispatch(
                            handleAssignTeamMemberThunk({
                              leadId: row.original.id,
                              memberId: member.id,
                            })
                          )
                        }
                      >
                        {member.name}
                      </Dropdown.Item>
                    ))
                  ) : (
                    <Dropdown.Item disabled>
                      {t("leadsTable.columns.noTeamMembers")}
                    </Dropdown.Item>
                  )}
                </Dropdown.Menu>
              </Dropdown>
            </>
          );
        },
      },
      {
        Header: t("leadsTable.columns.reassign"),
        accessor: "reassign",
        Cell: ({ row }) => {
          const assignedTo = row.original.assignedTo;

          if (!assignedTo) {
            return "-";
          }

          if (!currentUserPermissions?.includes("lead-edit")) {
            return assignedTo;
          }

          // Dropdown to choose another member (excluding current)
          const availableMembers = filteredTeamMembers?.filter(
            (m) => m.name !== assignedTo
          );

          return (
            <Dropdown>
              <Dropdown.Toggle
                variant="light"
                id="reassign-dropdown"
                className={"team-actions-button p-0 rounded-3"}
              >
                <div className={"assign-client-icon m-0"}>
                  <FaUserCog size={20} />
                </div>
              </Dropdown.Toggle>
              <Dropdown.Menu className={"team-actions-menu"} container="body">
                {availableMembers && availableMembers.length > 0 ? (
                  availableMembers.map((member) => (
                    <Dropdown.Item
                      key={member.id}
                      className={
                        "d-flex justify-content-between align-items-center text-secondary"
                      }
                      onClick={() =>
                        dispatch(
                          handleAssignTeamMemberThunk({
                            leadId: row.original.id,
                            memberId: member.id,
                          })
                        )
                      }
                    >
                      {member.name}
                    </Dropdown.Item>
                  ))
                ) : (
                  <Dropdown.Item disabled>
                    {t("leadsTable.columns.noTeamMembers")}
                  </Dropdown.Item>
                )}
              </Dropdown.Menu>
            </Dropdown>
          );
        },
      },
      {
        Header: t("leadsTable.columns.source"),
        accessor: "source",
        Cell: ({ row }) => {
          const source = row.original.source;
          const IconComponent = sourceToIcon[source] || null;

          return (
            <div className={"mx-auto social-icon-container"}>
              {IconComponent && <ReactSVG src={IconComponent} />}
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.status"),
        accessor: "status",
        Cell: ({ row }) => {
          const status = row?.original?.status;
          const statusMapping = {
            0: {
              label: t("status.pending"),
              className: "status-badge--pending",
            },
            1: {
              label: t("status.inProgress"),
              className: "status-badge--in-progress",
            },
            2: {
              label: t("status.completed"),
              className: "status-badge--completed",
            },
            3: {
              label: t("status.rejected"),
              className: "status-badge--rejected",
            },
            4: {
              label: t("status.wrongLead"),
              className: "status-badge--wrong-lead",
            },
            5: {
              label: t("status.notQualified"),
              className: "status-badge--not-qualified",
            },
            6: {
              label: t("status.noCommunication"),
              className: "status-badge--no-communication",
            },
            7: { label: t("status.booked"), className: "status-badge--booked" },
            8: {
              label: t("status.bookedReserved"),
              className: "status-badge--booked-reserved",
            },
            9: {
              label: t("status.canceled"),
              className: "status-badge--canceled",
            },
            10: {
              label: t("status.quotation"),
              className: "status-badge--quotation-sent",
            },
            11: {
              label: t("status.assigned"),
              className: "status-badge--in-progress",
            },
          };

          const { label, className } = statusMapping[status] || {
            label: "Unknown",
            className: "status-badge--unknown",
          };

          return (
            <div
              className={`status-badge ${className} rounded-pill p-1`}
              style={{ fontSize: "0.875rem", fontWeight: "600" }}
            >
              {label}
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.service"),
        accessor: "service",
      },
      {
        Header: t("leadsTable.columns.pageName"),
        accessor: "pageName",
        Cell: ({ row }) => {
          const pageName = row.original.pageName;
          return (
            <>
              <div
                className={`one-line page-name${row.original.id} mx-auto`}
                style={{ maxWidth: "150px" }}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(row.original.id)
                }
              >
                {pageName}
              </div>
              <Tooltip
                anchorSelect={`.page-name${row.original.id}`}
                content={pageName}
                className={"bg-dark text-white"}
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
              />
            </>
          );
        },
      },
      //     {
      //     Header: "Form Name", accessor: "formName", Cell: ({row}) => {
      //         const formName = row.original.formName;
      //         return (<>
      //                 <div
      //                     className={`one-line form-name${row.original.id} mx-auto`}
      //                     style={{maxWidth: "150px"}}
      //                     onTouchStart={() => !isHoverSupported && handleTouchStart(row.original.id)}
      //                 >
      //                     {formName}
      //                 </div>
      //                 <Tooltip
      //                     anchorSelect={`.form-name${row.original.id}`}
      //                     content={formName}
      //                     className={"bg-dark text-white"}
      //                     isOpen={isHoverSupported ? undefined : tooltipVisible[row.original.id]}
      //                 />
      //             </>);
      //     }
      // },
      // {
      //     Header: "Created By", accessor: "createdBy",
      // },
      {
        Header: t("leadsTable.columns.lastActivity"),
        accessor: "lastActivity",
        Cell: ({ row }) => {
          const tooltipId = `lastActivity_${row.id}`;
          const logs = Array.isArray(row.original?.logs)
            ? row.original.logs.slice(-3)
            : [row.original.logs];
          const lastLog = logs[0];

          if (!lastLog) {
            return null;
          }
          return (
            <Link
              to={`/leads/${row.original.id}`}
              data-for={tooltipId}
              data-tooltip-id={tooltipId}
              onTouchStart={() =>
                !isHoverSupported && handleTouchStart(row.original.id)
              }
            >
              <div className={"activity-button"}>
                {lastLog?.action === 1 && <MdAssignmentTurnedIn size={20} />}
                {lastLog?.action === 2 && <FaPhone size={20} />}
                {lastLog?.action === 3 && <FaCalendar size={20} />}
                {lastLog?.action === 4 && <FaEnvelope size={20} />}
              </div>
              <Tooltip
                id={tooltipId}
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
                className={"logs-tooltip-container"}
                content={
                  <>
                    <div className={"logs-list-container"}>
                      {logs?.map((log, index) => (
                        <div key={index} className={"log-container"}>
                          <div className={"d-flex justify-content-center my-2"}>
                            {log?.action === 1 && (
                              <MdAssignmentTurnedIn
                                size={20}
                                className={"mainColor"}
                              />
                            )}
                            {log?.action === 2 && (
                              <FaPhone size={20} className={"mainColor"} />
                            )}
                            {log?.action === 3 && (
                              <FaCalendar size={20} className={"mainColor"} />
                            )}
                            {log?.action === 4 && (
                              <FaEnvelope size={20} className={"mainColor"} />
                            )}
                          </div>
                          <p className={"opacity-50"}>
                            {log?.result || log?.note}
                          </p>
                          {/*<p className={"fw-bold"}>By: {log?.owner}</p>*/}
                        </div>
                      ))}
                    </div>
                  </>
                }
                place={"left-start"}
                events={["hover"]}
              />
            </Link>
          );
        },
      },
      {
        Header: t("leadsTable.columns.createdAt"),
        accessor: "createdAt",
        Cell: ({ value, row }) => {
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              const formattedDate = format(parsedDate, "yyyy-MM-dd HH:mm:ss");
              return (
                <Link to={`/leads/${row.original.id}`}>{formattedDate}</Link>
              );
            }
          }
          return <Link to={`/leads/${row.original.id}`}>{value}</Link>;
        },
      },
      {
        Header: t("leadsTable.columns.actions"),
        Cell: ({ row }) => (
          <div className={"d-flex justify-content-center"}>
            {currentUserPermissions?.includes("lead-edit") ? (
              <Link
                to={`/leads/${row.original.id}`}
                className={"me-3 shadow-sm rounded-2 p-1"}
              >
                <MdEdit size={20} className={"text-dark"} />
              </Link>
            ) : null}
            {currentUserPermissions?.includes("lead-delete") ? (
              <div className={"shadow-sm rounded-2 p-1"}>
                <PiTrashFill
                  onClick={() => handleDelete(row.original.id)}
                  size={20}
                  className={"text-danger"}
                />
              </div>
            ) : null}
          </div>
        ),
      },
    ],
    [teamMembers, t]
  );

  // Determine default sort: non-admin (has parent_id) => sort updatedAt desc
  const initialSortBy = [];

  const hiddenColumns = useMemo(() => ["updatedAt", "clientId"], []);

  return (
    <>
      <DataTableComponent
        columns={columns}
        data={data}
        initialSortBy={initialSortBy}
        hiddenColumns={hiddenColumns}
        handleFilterStatus={handleFilterStatus}
        leadStatusCounts={leadStatusCounts}
        loading={loading}
        handleFilterLeads={handleFilterLeads}
        setLeadStatusCounts={setLeadStatusCounts}
      />
      <CenteredModal
        show={showCenteredModal}
        children={
          <DeleteLeadFromTable
            leadId={selectedLeadId}
            setShowCenteredModal={setShowCenteredModal}
          />
        }
        onHide={() => {
          setShowCenteredModal(false);
          setSelectedLeadId(null);
        }}
      />
    </>
  );
}

export default FilterTableLeadsComponent;
