import React, { useEffect, useState } from "react";
import "./marketing.css";
import getMarketingDetailsApi from "../../services/marketing";
import MarketingHierarchySwiper from "./MarketingHierarchySwiper";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { Button } from "react-bootstrap";

const removeNullFields = (obj) => {
    if (!obj || typeof obj !== "object") return obj;
    return Object.fromEntries(
        Object.entries(obj)
            .map(([key, value]) => {
                if (typeof value === "number" && (isNaN(value) || !isFinite(value))) {
                    return [key, 0];
                }
                if (value === null || value === undefined) {
                    return [key, ""];
                }
                return [key, value];
            })
    );
};

const transformResponseToIcicleData = (response) => {
    if (!response?.data?.sources || response.data.sources.length === 0) {
        return { name: "Root", title: "Root Node", children: [] };
    }

    const aggregateMetrics = (node) => {
        if (!node.children || node.children.length === 0) {
            return node.metrics || {};
        }

        let aggregatedMetrics = {
            clicks: 0,
            reach: 0,
            cpm: 0,
            spend: 0,
        };

        node.children.forEach((child) => {
            const childMetrics = aggregateMetrics(child);
            aggregatedMetrics.clicks += parseInt(childMetrics.clicks) || 0;
            aggregatedMetrics.reach += parseInt(childMetrics.reach) || 0;
            aggregatedMetrics.spend += parseFloat(childMetrics.spend) || 0;
        });

        if (aggregatedMetrics.reach > 0) {
            aggregatedMetrics.cpm = (
                (aggregatedMetrics.spend / aggregatedMetrics.reach) *
                1000
            ).toFixed(2);
        }

        node.metrics = {
            ...node.metrics,
            clicks: aggregatedMetrics.clicks,
            reach: aggregatedMetrics.reach,
            spend: aggregatedMetrics.spend.toFixed(2),
            cpm: aggregatedMetrics.cpm || "N/A",
        };

        return node.metrics;
    };

    const data = {
        name: "Root",
        title: "Marketing Performance",
        children: response.data.sources.map((source) =>
            removeNullFields({
                name: source.name || "Unknown Source",
                title: `Source: ${source.name || "Unknown Source"}`,
                children: source.pages?.map((page) =>
                    removeNullFields({
                        name: page?.name || "Unknown Page",
                        title: `Page: ${page?.name || "Unknown Page"}`,
                        value: page?.value,
                        children: page?.campaigns?.map((campaign) =>
                            removeNullFields({
                                name: campaign?.name || "Unknown Campaign",
                                title: `Campaign: ${campaign?.name || "Unknown Campaign"}`,
                                value: campaign?.spend,
                                metrics: {
                                    clicks: campaign?.clicks || 0,
                                    reach: campaign?.reach || 0,
                                    spend: campaign?.spend || 0,
                                    cpm: campaign?.cpm || "N/A",
                                },
                                date_start: campaign?.date_start || null,
                                date_stop: campaign?.date_stop || null,
                                status: campaign?.status || null,
                                children: campaign?.adsets?.map((adset) =>
                                    removeNullFields({
                                        name: adset?.name || "Unknown Adset",
                                        title: `Adset: ${adset?.name || "Unknown Adset"}`,
                                        value: adset?.spend,
                                        metrics: {
                                            clicks: adset?.clicks || 0,
                                            reach: adset?.reach || 0,
                                            spend: adset?.spend || 0,
                                            cpm: adset?.cpm || "N/A",
                                        },
                                        date_start: adset?.date_start || null,
                                        date_stop: adset?.date_stop || null,
                                        status: adset?.status || null,
                                        children: adset?.ads?.map((ad) =>
                                            removeNullFields({
                                                name: ad?.name || "Unknown Ad",
                                                title: `Ad: ${ad?.name || "Unknown Ad"}`,
                                                value: ad?.spend,
                                                metrics: {
                                                    clicks: ad?.clicks || 0,
                                                    reach: ad?.reach || 0,
                                                    spend: ad?.spend || 0,
                                                    cpm: ad?.cpm || "N/A",
                                                },
                                                date_start: ad?.date_start || null,
                                                date_stop: ad?.date_stop || null,
                                                status: ad?.status || null,
                                            })
                                        ),
                                    })
                                ),
                            })
                        ),
                    })
                ),
            })
        ),
    };

    aggregateMetrics(data); // Aggregate metrics after constructing the hierarchy
    return data;
};

const MarketingDetails = () => {
    const { user } = useSelector((state) => state.auth);
    const [chartData, setChartData] = useState({ name: "Root", children: [] });
    const [loading, setLoading] = useState(true);
    const [selectedPlatform, setSelectedPlatform] = useState(null);
    const [selectedPage, setSelectedPage] = useState(null);
    const [selectedCampaign, setSelectedCampaign] = useState(null);
    const [selectedAdSet, setSelectedAdSet] = useState(null);

    useEffect(() => {
        if(user?.user?.package_id === 1) return;
        const fetchData = async () => {
            try {
                const response = await getMarketingDetailsApi();
                const sanitizedResponse = removeNullFields(response);
                const transformedData = transformResponseToIcicleData(sanitizedResponse);
                setChartData(transformedData);
            } catch (error) {
                console.error("Error fetching data:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    return (
        user?.user?.package_id === 1 ? (
                <div className="d-flex justify-content-center align-items-center gap-4 fs-5">
                <div className="radial-gradient-border">Subscribe to track your campaign results and achieve success faster!</div>
                <Link to={"/packages"}><Button className="submit-btn">Upgrade Now</Button>
                </Link>
                </div>
        ) :
        <div>
            <center className="fs-2 fw-bold mb-4">Marketing Details</center>
            {loading ? (
                <p>Loading...</p>
            ) : (
                <MarketingHierarchySwiper
                    data={chartData.children}
                    selectedPlatform={selectedPlatform}
                    selectedPage={selectedPage}
                    selectedCampaign={selectedCampaign}
                    selectedAdSet={selectedAdSet}
                    onPlatformSelect={(platform) => setSelectedPlatform(platform)}
                    onPageSelect={(page) => setSelectedPage(page)}
                    onCampaignSelect={(campaign) => setSelectedCampaign(campaign)}
                    onAdSetSelect={(adSet) => setSelectedAdSet(adSet)}
                />
            )}
        </div>
    );
};

export default MarketingDetails;
