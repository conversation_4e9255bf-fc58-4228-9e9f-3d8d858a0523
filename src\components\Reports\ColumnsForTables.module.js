import { <PERSON>lt<PERSON> } from "react-tooltip";
import { format, parseISO } from "date-fns";
import { ReactSVG } from "react-svg";
import FacebookIcon from "../../assets/media/Icons/facebook.svg";
import GoogleAdsIcon from "../../assets/media/Icons/googleads.svg";
import SnapChatIcon from "../../assets/media/Icons/snapchat.svg";
import TikTokIcon from "../../assets/media/Icons/tiktok.svg";
import Instagram from "../../assets/media/Icons/instagram.svg";
import LinkedInIcon from "../../assets/media/Icons/linkedin.svg";
import phone from "../../assets/media/Icons/phone.svg";
import whatsapp from "../../assets/media/Icons/whatsapp.svg";
import GlobeIcon from "../../assets/media/Icons/globe.svg";
import { IoSparklesSharp } from "react-icons/io5";
import { <PERSON> } from "react-router-dom";
import MessengerIcon from "../../assets/media/Icons/messenger.svg";
import InstagramIcon from "../../assets/media/Icons/instagram.svg";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { Badge, Button, Form, Spinner } from "react-bootstrap";
import { FaEye } from "react-icons/fa";
import StatusDropdown from './StatusDropdown';
import PriorityDropdown from './PriorityDropdown';

const sourceToIcon = {
  1: FacebookIcon,
  2: GoogleAdsIcon,
  3: SnapChatIcon,
  4: TikTokIcon,
  5: Instagram,
  6: LinkedInIcon,
  7: phone,
  8: whatsapp,
  9: GlobeIcon,
  10: MessengerIcon,
  11: InstagramIcon
};

export const createTranslatedColumns = (t) => {
  const leadAssignmentColumns = [
    {
      Header: "#",
      accessor: "leadId",
      Cell: ({ row }) => <Link to={`/leads/${row.original.id}`}>{row.index + 1}</Link>,
    },
    {
      Header: t('tables.headers.name'),
      accessor: "name",
      Cell: ({ row }) => {
        const name = row.original.name;
        return (
          <>
            <Link
              className={`one-line lead-name-reports${row.original.id}`}
              style={{ maxWidth: "200px" }}
              to={`/leads/${row.original.id}`}>
              {row.original.status === 0 ? <IoSparklesSharp className={"mainColor"} /> : null}  {name}
            </Link>
            <Tooltip
              anchorSelect={`.lead-name-reports${row.original.id}`}
              content={name}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: "phone",
      Cell: ({ row }) => {
        const phone = row.original.phone;
        return (
          <Link to={`/leads/${row.original.id}`} className={"text-nowrap"}>
            {phone}
          </Link>
        );
      }
    },
    {
      Header: t('tables.headers.assignedTo'),
      accessor: "assigned_name",
      Cell: ({ row }) => {
        const assignedName = row.original.assigned_name;
        return (
          <Link to={`/leads/${row.original.id}`} className={"text-nowrap"}>
            {assignedName}
          </Link>
        );
      }
    },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <Link to={`/leads/${row.original.id}`} className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </Link>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value, row }) => {
        if (value) {
          const parsedDate = new Date(value);
          if (!isNaN(parsedDate.getTime())) {
            const formattedDate = format(parsedDate, "yyyy-MM-dd HH:mm");
            return <Link to={`/leads/${row.original.id}`}>{formattedDate}</Link>;
          }
        }
        return <Link to={`/leads/${row.original.id}`}>{value}</Link>;
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = Number(row?.original?.status);
        return (
          <Link
            to={`/leads/${row.original.id}`}
            className={`${status === 0 ? "pending-status" : ""} ${status === 1 ? "in-progress-status" : ""} ${status === 2 ? "completed-status" : ""} ${status === 3 ? "rejected-status" : ""} ${status === 4 ? "wrong-lead-status" : ""} ${status === 5 ? "not-qualified-status" : ""} ${status === 6 ? "no-communication-status" : ""} ${status === 7 ? "booked-status" : ""} ${status === 8 ? "booked-reserved-status" : ""} ${status === 9 ? "canceled-status" : ""} rounded-pill p-1 fw-bold`}
            style={{ fontSize: "0.8rem" }}
          >
            {status === 0 && t('status.pending')}
            {status === 1 && t('status.inProgress')}
            {status === 11 && t('status.assigned')}
            {status === 2 && t('status.completed')}
            {status === 3 && t('status.rejected')}
            {status === 4 && t('status.wrongLead')}
            {status === 5 && t('status.notQualified')}
            {status === 6 && t('status.noCommunication')}
            {status === 7 && t('status.booked')}
            {status === 8 && t('status.bookedReserved')}
            {status === 9 && t('status.canceled')}
            {status === 10 && t('status.quotation')}
          </Link>
        );
      },
    },
  ];

  const teamMembersColumns = [
    {
      Header: "#",
      accessor: "teamMemberId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.member'),
      accessor: "name",
      Cell: ({ row }) => {
        const name = row.original.name;
        return (
          <>
            <div
              className={`one-line tm-name-${row.original.id}`}
              style={{ maxWidth: "150px" }}
            >
              {name}
            </div>
            <Tooltip
              anchorSelect={`.tm-name-${row.original.id}`}
              content={name}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.role'),
      accessor: "role",
      Cell: ({ row }) => {
        const Role = row.original.role;
        const roleKey = {
          0: 'roles.admin',
          1: 'roles.admin',
          2: 'roles.moderator',
          3: 'roles.sales',
          4: 'roles.accountant',
          5: 'roles.teamMember'
        }[Role] || 'roles.moderator';

        return (
          <div className={"d-flex justify-content-center align-items-center"}>
            <div className={"shadow-sm rounded-2 p-1"}>{t(roleKey)}</div>
          </div>
        );
      },
    },
    { Header: t('tables.headers.status'), accessor: "status" },
    { Header: t('tables.headers.totalLeads'), accessor: "total_leads" },
    { Header: t('tables.headers.completed'), accessor: "completed_leads" },
    { Header: t('tables.headers.inProgress'), accessor: "in_progress_leads" },
    { Header: t('tables.headers.rejected'), accessor: "rejected_leads" },
    { Header: t('tables.headers.booked'), accessor: "booked_leads" },
    { Header: t('tables.headers.assigned'), accessor: "assigned_leads" },
  ];

  const salesColumns = [
    {
      Header: "#",
      accessor: "salesId",
      Cell: ({ row }) => <Link to={`/leads/${row.original.lead_id}`}>{row.index + 1}</Link>,
    },
    {
      Header: t('tables.headers.member'),
      accessor: "team_name",
      Cell: ({ row }) => {
        const leadId = row.original.lead_id;
        const value = row.original.team_name;
        return (
          <>
            <Link
              to={`/leads/${leadId}`}
              className={`one-line sales-tm-${row.index}`}
              style={{ maxWidth: '140px' }}
            >
              {value}
            </Link>
            <Tooltip
              anchorSelect={`.sales-tm-${row.index}`}
              content={value}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: t('tables.headers.lead'),
      accessor: "lead_name",
      Cell: ({ row }) => {
        const leadId = row.original.lead_id;
        const value = row.original.lead_name;
        return (
          <>
            <Link
              to={`/leads/${leadId}`}
              className={`one-line sales-lead-${row.original.lead_id}`}
              style={{ maxWidth: '140px' }}
            >
              {value}
            </Link>
            <Tooltip
              anchorSelect={`.sales-lead-${row.original.lead_id}`}
              content={value}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: t('tables.headers.inProgress'),
      accessor: "in_progress",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        return (
          <Link to={`/leads/${row.original.lead_id}`}
            className={"one-line mx-auto"} title={row?.original?.Act_note}
            style={{ maxWidth: "200px" }}>
            {status === 1 ? row?.original?.Act_note : "-"}
          </Link>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "create",
      Cell: ({ row }) => {
        const dateWithoutSeconds = <Link to={`/leads/${row.original.lead_id}`}>{row?.original?.Act_created_at.slice(0, -3)}</Link>
        return (dateWithoutSeconds)
      }
    },
    {
      Header: t('tables.headers.completed'),
      accessor: "completed",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        const dateWithoutSeconds = row?.original?.Act_created_at.slice(0, -3);
        return (
          <Link to={`/leads/${row.original.lead_id}`}>{status === 2 ? dateWithoutSeconds : "-"} </Link>
        );
      },
    },
    {
      Header: t('tables.headers.rejected'),
      accessor: "rejected",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        const dateWithoutSeconds = row?.original?.Act_created_at.slice(0, -3);
        return (
          <Link to={`/leads/${row.original.lead_id}`}>{status === 3 ? dateWithoutSeconds : "-"} </Link>
        );
      },
    },
  ];

  const statisticsForTMColumns = [
    {
      Header: "#",
      accessor: "teamMemberId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    { Header: t('tables.headers.lead'), accessor: "name" },
    { Header: t('tables.headers.phone'), accessor: "phone" },
    { Header: t('tables.headers.pageName'), accessor: "page_name" },
    { Header: t('tables.headers.formName'), accessor: "form_name" },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <div className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = row?.original?.status;
        return (
          <div
            className={`${status === 0 && "pending-status"} ${status === 1 ? "in-progress-status" : null} ${status === 2 && "completed-status"} ${status === 3 && "rejected-status"} ${status === 4 && "wrong-lead-status"} ${status === 5 && "not-qualified-status"} ${status === 6 && "no-communication-status"} ${status === 7 && "booked-status"} ${status === 8 && "booked-reserved-status"} ${status === 9 && "canceled-status"} rounded-pill p-1 fw-bold`}
            style={{ fontSize: "0.8rem" }}
          >
            {status === 0 && t('status.pending')}
            {status === 11 && t('status.assigned')}
            {status === 1 && t('status.inProgress')}
            {status === 2 && t('status.completed')}
            {status === 3 && t('status.rejected')}
            {status === 4 && t('status.wrongLead')}
            {status === 5 && t('status.notQualified')}
            {status === 6 && t('status.noCommunication')}
            {status === 7 && t('status.booked')}
            {status === 8 && t('status.bookedReserved')}
            {status === 9 && t('status.canceled')}
          </div>
        );
      },
    },
    { Header: t('tables.headers.amount'), accessor: "amount" },
  ];

  const newLeadsForAdmin = [
    {
      Header: t('tables.headers.id'),
      accessor: "leadId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.leadName'),
      accessor: "name",
      Cell: ({ row }) => {
        const leadName = row.original.name;
        return (
          <>
            <div className={`text-nowrap overflow-hidden text-center mx-auto one-line lead-name-${row.original.id}`} style={{ maxWidth: "200px" }}>
              {leadName}
            </div>
            <Tooltip
              anchorSelect={`.lead-name-${row.original.id}`}
              content={leadName}
              className={"bg-white text-dark"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.email'),
      accessor: "email",
      Cell: ({ row }) => {
        const email = row.original.email;
        return (
          <>
            <div className={`text-nowrap overflow-hidden text-center mx-auto one-line lead-email-${row.original.id}`} style={{ maxWidth: "200px" }}>
              {email}
            </div>
            <Tooltip
              anchorSelect={`.lead-email-${row.original.id}`}
              content={email}
              className={"bg-white text-dark"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: "phone",
      Cell: ({ row }) => {
        const phone = row.original.phone;
        return (
          <div className={"text-nowrap"}>
            {phone}
          </div>
        )
      },
    },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <div className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.createdBy'),
      accessor: "create.name",
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ value, row }) => {
        const statusName = (value) => {
          switch (value) {
            case 0:
              return t('status.pending');
            case 1:
              return t('status.inProgress');
            case 2:
              return t('status.completed');
            case 3:
              return t('status.rejected');
            case 4:
              return t('status.wrongLead');
            case 5:
              return t('status.notQualified');
            case 6:
              return t('status.noCommunication');
            case 7:
              return t('status.booked');
            case 8:
              return t('status.bookedReserved');
            case 9:
              return t('status.canceled');
            case 10:
              return t('status.quotation')
            case 11:
              return t('status.assigned')
            default:
              return t('status.unknown');
          }
        };

        return (
          row.original.status === 2 ?
            <span className="completed-status">{statusName(value)}</span> :
            row.original.status === 0 ?
              <span className="on-hold-status">{statusName(value)}</span> :
              row.original.status === 1 ?
                <span className="in-progress-status">{statusName(value)}</span> :
                row.original.status === 3 || row.original.status === 4 || row.original.status === 5 || row.original.status === 6 ?
                  <span className="not-started-status">{statusName(value)}</span> :
                  <span>{statusName(value)}</span>
        );
      }
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value }) => {
        if (value) {
          const parsedDate = parseISO(value);
          const formattedDate = format(parsedDate, 'yyyy-MM-dd');
          return <div>{formattedDate}</div>
        }
      },
    }
  ];

  const ticketsColumns = (isLoadingTicketDetails, loadingTicketId, handleViewTicket) => [
    {
      Header: t('tables.headers.id'),
      accessor: "ticketId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.ticketTitle'),
      accessor: "title",
      Cell: ({ row }) => {
        const title = row.original.title;
        return (
          <div className={`text-nowrap overflow-hidden text-center mx-auto one-line ticket-title-${row.original.id}`} style={{ maxWidth: "200px" }}>
            {title}
          </div>
        )
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = row.original.status;

        // Define badge colors based on status
        const getStatusBadgeVariant = (status) => {
          switch (status?.toLowerCase()) {
            case "open":
              return "primary";
            case "in_progress":
              return "warning";
            case "closed":
            case "resolved":
              return "success";
            case "pending":
              return "info";
            case "cancelled":
              return "danger";
            default:
              return "secondary";
          }
        };

        return (
          <div className="text-center">
            <Badge
              bg={getStatusBadgeVariant(status)}
              className="px-3 py-2 d-inline-block"
              style={{ minWidth: "100px" }}
            >
              {(() => {
                // Replace underscores with spaces and capitalize each word
                if (!status) return "";
                return status
                  .replace(/_/g, " ")
                  .split(" ")
                  .map((word) =>
                    word.length > 0
                      ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                      : ""
                  )
                  .join(" ");
              })()}
            </Badge>
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.priority'),
      accessor: "priority",
      Cell: ({ row }) => {
        const priority = row.original.priority;

        // Define badge colors based on priority
        const getPriorityBadgeVariant = (priority) => {
          switch (priority?.toLowerCase()) {
            case "high":
              return "danger";
            case "medium":
              return "warning";
            case "low":
              return "success";
            default:
              return "secondary";
          }
        };

        return (
          <div className="text-center">
            <Badge
              bg={getPriorityBadgeVariant(priority)}
              className="px-3 py-2 d-inline-block"
              style={{ minWidth: "100px" }}
            >
              {priority}
            </Badge>
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value }) => {
        if (value) {
          const parsedDate = parseISO(value);
          const formattedDate = format(parsedDate, 'yyyy-MM-dd');
          return <div>{formattedDate}</div>
        }
      },
    },
    {
      Header: t('tables.headers.actions'),
      Cell: ({ row }) => {
        const isThisTicketLoading = isLoadingTicketDetails && loadingTicketId === row.original.id;

        return (
          <div className="d-flex justify-content-center">
            <Button
              variant="info"
              size="sm"
              className="view-ticket-btn"
              disabled={isThisTicketLoading}
              onClick={(e) => {
                e.stopPropagation();
                handleViewTicket(row.original.id);
              }}
            >
              {isThisTicketLoading ? (
                <Spinner animation="border" size="sm" className="me-1" />
              ) : (
                <FaEye className="me-1" />
              )}
              {t('buttons.view')}
            </Button>
          </div>
        );
      }
    }
  ];

  return {
    leadAssignmentColumns,
    teamMembersColumns,
    salesColumns,
    statisticsForTMColumns,
    newLeadsForAdmin,
    ticketsColumns,
  };
};

export const useTranslatedColumns = () => {
  const { t } = useTranslation();

  // Use useMemo to prevent recreating the columns on every render
  const columns = useMemo(() => createTranslatedColumns(t), [t]);

  return columns;
};
