import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/addons/dragAndDrop/styles.css";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { useEffect, useState, useRef } from "react";
import { Col, Row } from "react-bootstrap";
import { IoChevronBack, IoChevronForward } from "react-icons/io5";
import activityService from "../../../services/activities";
import { Link } from "react-router-dom";
import LoadingDotsAnimation from "../../LoadingAnimation/LoadingDotsAnimation";
import "./meeting-calendar.css";
import { useTranslation } from "react-i18next";

const localizer = momentLocalizer(moment);

const CustomToolbar = (toolbar) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === "ar";

  const goToBack = () => {
    toolbar.onNavigate("PREV");
  };
  const goToNext = () => {
    toolbar.onNavigate("NEXT");
  };
  const goToToday = () => {
    toolbar.onNavigate("TODAY");
  };
  const goToWeek = () => {
    toolbar.onView("week");
  };
  const goToDay = () => {
    toolbar.onView("day");
  };
  const goToMonth = () => {
    toolbar.onView("month");
  };
  const goToAgenda = () => {
    toolbar.onView("agenda");
  };
  return (
    <div className="rbc-toolbar justify-content-around">
      <button type="button" className="btn btn-secondary" onClick={goToToday}>
        {t("calendar.buttons.today")}
      </button>
      <div className={"d-flex justify-content-between align-items-center"}>
        <div role={"button"} onClick={isRTL ? goToNext : goToBack}>
          {isRTL ? (
            <IoChevronForward
              size={30}
              className={"bg-white rounded-circle p-1 shadow"}
            />
          ) : (
            <IoChevronBack
              size={30}
              className={"bg-white rounded-circle p-1 shadow"}
            />
          )}
        </div>
        <span className="rbc-toolbar-label">{toolbar.label}</span>
        <div role={"button"} onClick={isRTL ? goToBack : goToNext}>
          {isRTL ? (
            <IoChevronBack
              size={30}
              className={"bg-white rounded-circle p-1 shadow"}
            />
          ) : (
            <IoChevronForward
              size={30}
              className={"bg-white rounded-circle p-1 shadow"}
            />
          )}
        </div>
      </div>
      <div className="btn-group ml-2" role="group">
        <button type="button" className="btn btn-secondary" onClick={goToDay}>
          {t("calendar.buttons.day")}
        </button>
        <button type="button" className="btn btn-secondary" onClick={goToWeek}>
          {t("calendar.buttons.week")}
        </button>
        <button type="button" className="btn btn-secondary" onClick={goToMonth}>
          {t("calendar.buttons.month")}
        </button>
        <button
          type={"button"}
          className={"btn btn-secondary"}
          onClick={goToAgenda}
        >
          {t("calendar.buttons.agenda")}
        </button>
      </div>
    </div>
  );
};

const MeetingCalendarComponent = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === "ar";

  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentView, setCurrentView] = useState("agenda");
  const [currentCalendarDate, setCurrentCalendarDate] = useState(
    moment().startOf("day").toDate()
  );
  const [startDate, setStartDate] = useState(moment().startOf("day").toDate());
  const [endDate, setEndDate] = useState(
    moment().startOf("day").add(30, "days").endOf("day").toDate()
  );
  const [quotaExceeded, setQuotaExceeded] = useState(false);
  const calendarRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(null);
  const [scrollLeft, setScrollLeft] = useState(null);

  const handleTouchStart = (e) => {
    if (!calendarRef.current) return;
    const touch = e.touches[0];
    const calendar = calendarRef.current;
    const isScrollableWidth = calendar.scrollWidth > calendar.clientWidth;

    // Only enable horizontal touch scrolling if content is wider than container
    if (isScrollableWidth) {
      setIsDragging(true);
      setStartX(touch.pageX - calendar.offsetLeft);
      setScrollLeft(calendar.scrollLeft);
    }
  };

  const handleTouchMove = (e) => {
    if (!isDragging || !calendarRef.current) return;

    // Don't prevent default vertical scrolling
    const touch = e.touches[0];
    const x = touch.pageX - calendarRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    calendarRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const formattedStartDate = moment(startDate).format("YYYY-MM-DD");
        const formattedEndDate = moment(endDate).format("YYYY-MM-DD");
        const response = await activityService.getLastActivitiesApi({
          start_date: formattedStartDate,
          end_date: formattedEndDate,
        });
        const formattedEvents = response?.data?.leads?.map((activity) => {
          const nextDate = activity?.activities?.next_date
            ? new Date(activity.activities.next_date)
            : null;
          const end = nextDate
            ? new Date(nextDate.getTime() + 30 * 60000)
            : null;
          const status = activity.status;
          return {
            ...activity?.activities,
            id: activity?.activities?.id,
            start: nextDate,
            end,
            title: activity?.activities?.result || activity?.activities?.note,
            description: activity?.activities?.note,
            status: status,
            assignedTo: activity?.assigned_to,
            leadName: activity?.name,
          };
        });
        setEvents(formattedEvents);
        response?.data?.quota === 0 && setQuotaExceeded(true);
        setLoading(false);
      } catch (error) {
        console.error(error);
        setLoading(false);
      }
    };
    fetchData();
  }, [startDate, endDate]);

  const handleNavigate = (date, view) => {
    const newDate = moment(date).toDate();
    let newStartDate, newEndDate;
    if (view === "agenda") {
      newStartDate = moment(newDate).startOf("day").toDate();
      newEndDate = moment(newStartDate).add(30, "days").endOf("day").toDate();
    } else {
      newStartDate = moment(newDate).startOf(view).toDate();
      newEndDate = moment(newDate).endOf(view).toDate();
      if (view === "week") {
        newEndDate = moment(newStartDate).add(6, "days").endOf("day").toDate();
      } else if (view === "day") {
        newEndDate = moment(newStartDate).endOf("day").toDate();
      }
    }
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    setCurrentCalendarDate(newDate);
  };

  const handleViewChange = (view) => {
    const currentDate = moment(currentCalendarDate).toDate();
    let newStartDate, newEndDate;
    if (view === "agenda") {
      newStartDate = moment(currentDate).startOf("day").toDate();
      newEndDate = moment(newStartDate).add(30, "days").endOf("day").toDate();
    } else {
      newStartDate = moment(currentDate).startOf(view).toDate();
      newEndDate = moment(currentDate).endOf(view).toDate();
      if (view === "week") {
        newEndDate = moment(newStartDate).add(6, "days").endOf("day").toDate();
      } else if (view === "day") {
        newEndDate = moment(newStartDate).endOf("day").toDate();
      }
    }
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    setCurrentView(view);
    setCurrentCalendarDate(newStartDate);
  };

  const getEventColors = (status) => {
    const backgroundColors = {
      0: "rgba(186, 189, 6, 0.10)", // Pending
      1: "rgba(249, 119, 0, 0.10)", // In Progress
      2: "rgba(146, 192, 32, 0.10)", // Completed
      3: "rgba(160, 6, 6, 0.10)", // Rejected
      4: "rgba(0, 128, 128, 0.10)", // Wrong Lead
      5: "rgba(128, 0, 128, 0.10)", // Not Qualified
      6: "rgba(0, 0, 255, 0.10)", // No Communication
      7: "rgba(255, 0, 255, 0.10)", // Booked
      8: "rgba(27,71,248,0.2)", // Booked and Reserved
      9: "rgba(128, 128, 128, 0.10)", // Canceled
      10: "rgba(0, 0, 0, 0.10)", // Quotation
      // Add more colors as needed
    };
    const textColors = {
      0: "#BABD06", // Pending
      1: "#F97700", // In Progress
      2: "#92C020", // Completed
      3: "#A00606", // Rejected
      4: "#008080", // Wrong Lead
      5: "#800080", // Not Qualified
      6: "#0000FF", // No Communication
      7: "#FF00FF", // Booked
      8: "#1b47f8", // Booked and Reserved
      9: "#808080", // Canceled
      10: "#333333", // Quotation
      // Add more colors as needed
    };
    return {
      backgroundColor: backgroundColors[status] || "rgba(255, 255, 255, 0.10)", // Default background
      textColor: textColors[status] || "#000000", // Default text color
    };
  };

  const CustomEvent = ({ event }) => {
    const { backgroundColor, textColor } = getEventColors(event.status);
    const eventStyle = {
      backgroundColor,
      borderRadius: "6px",
      backdropFilter: "blur(2px)",
      opacity: 0.8,
      color: textColor,
      display: "block",
      padding: "5px",
      cursor: "pointer",
    };
    return (
      <Link to={`/leads/${event.lead_id}`}>
        <div style={eventStyle}>
          <div className="rbc-event-label fs-6 fw-bold">
            <div>{event.title || t("calendar.event.noTitle")}</div>
            <div>
              {event?.leadName}{" "}
              {event?.assignedTo ? (
                <span>
                  {t("calendar.event.appointmentWith")}{" "}
                  <span
                    className={"mainColor"}
                    dangerouslySetInnerHTML={{
                      __html: event?.assignedTo?.name,
                    }}
                  ></span>
                </span>
              ) : null}
            </div>
          </div>
        </div>
      </Link>
    );
  };

  return (
    <Row className={"meeting-calendar-container"} dir={isRTL ? "rtl" : "ltr"}>
      <Col lg={12} className={"p-4"}>
        {loading ? (
          <div className={"d-flex justify-content-center align-items-center"}>
            <div>{t("calendar.event.loading")}</div>
            <LoadingDotsAnimation />
          </div>
        ) : (
          <>
            {quotaExceeded && (
              <div className="quota-container">
                <div className="radial-gradient-border">
                  {t("calendar.event.quotaExceeded")}
                </div>
                <Link to={"/packages"}>
                  <button className="submit-btn">
                    {t("common.upgradeNow")}
                  </button>
                </Link>
              </div>
            )}
            <div
              className="rbc-calendar-wrapper"
              ref={calendarRef}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <Calendar
                localizer={localizer}
                events={events}
                dayLayoutAlgorithm={"no-overlap"}
                resizable
                date={currentCalendarDate}
                view={currentView}
                className={`calendar-container ${isDragging ? 'dragging' : ''}`}
                components={{
                  event: CustomEvent,
                  toolbar: CustomToolbar,
                }}
                onNavigate={(date) => handleNavigate(date, currentView)}
                onView={handleViewChange}
                messages={{
                  next: t("calendar.navigation.next"),
                  previous: t("calendar.navigation.previous"),
                  today: t("calendar.buttons.today"),
                  month: t("calendar.buttons.month"),
                  week: t("calendar.buttons.week"),
                  day: t("calendar.buttons.day"),
                  agenda: t("calendar.buttons.agenda"),
                  noEventsInRange: t("calendar.event.noEvents"),
                  allDay: t("calendar.event.allDay"),
                }}
                rtl={isRTL}
              />
            </div>
          </>
        )}
      </Col>
    </Row>
  );
};

export default MeetingCalendarComponent;
