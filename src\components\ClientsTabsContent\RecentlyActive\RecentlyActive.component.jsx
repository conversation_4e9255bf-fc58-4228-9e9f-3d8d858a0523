import {useMemo} from "react";
import clientPic from "../../../assets/media/Icons/Ellipse2.svg";
import clientPic2  from "../../../assets/media/Icons/Ellipse4.svg";
import clientPic3  from "../../../assets/media/Icons/Ellipse4.svg";
import clientPic4  from "../../../assets/media/Icons/Ellipse5.svg";
import clientPic5  from "../../../assets/media/Icons/Ellipse6.svg";
import clientPic6  from "../../../assets/media/Icons/Ellipse7.svg";
import DataTableRecentlyActiveComponent from "./DataTableRecentlyActive.component";
import {ReactSVG} from "react-svg";

const RecentlyActiveComponent = () => {
    const columns = useMemo(() => [
        {
            Header: ' ',
            Cell: ({row}) => (
                <ReactSVG src={row.original.profilePic} />
            ),
        }, {
            Header: "Name", accessor: "contactName",
        }, {
            Header: "Details", accessor: "details",
        },
        {
            Header: "Viewed Item", accessor: "viewedItem",
        },
        {
            Header: "Last Viewed", accessor: "lastViewed",
        }
    ], []);

    const data = useMemo(() => [
        {
            id: 1,
            contactName: "Savannah Nguyen",
            details: "Meet with officiants",
            viewedItem: "15 minutes ago",
            profilePic: clientPic,
            lastViewed: "5s",
        },
        {
            id: 2,
            contactName: "Follow Up 1",
            details: "John Doe",
            viewedItem: "Details 1",
            profilePic: clientPic2,
            lastViewed: "10s",
        },
        {
            id: 3,
            contactName: "Follow Up 2",
            details: "Jane Smith",
            viewedItem: "Details 2",
            profilePic: clientPic3,
            lastViewed: "15s",
        },
        {
            id: 4,
            contactName: "Follow Up 3",
            details: "Tom Johnson",
            viewedItem: "Details 3",
            profilePic: clientPic4,
            lastViewed: "20s",
        },
        {
            id: 5,
            contactName: "Follow Up 4",
            details: "Sara Lee",
            viewedItem: "Details 4",
            profilePic: clientPic5,
            lastViewed: "25s",
        },
        {
            id: 6,
            contactName: "Follow Up 5",
            details: "Mark Davis",
            viewedItem: "Details 5",
            profilePic: clientPic6,
            lastViewed: "30s",
        },
        {
            id: 7,
            contactName: "Follow Up 6",
            details: "Lisa Brown",
            viewedItem: "Details 6",
            profilePic: clientPic,
            lastViewed: "35s",
        },
        {
            id: 8,
            contactName: "Follow Up 7",
            details: "Jack Wilson",
            viewedItem: "Details 7",
            profilePic: clientPic2,
            lastViewed: "40s",
        },
        {
            id: 9,
            contactName: "Follow Up 8",
            details: "Emily Johnson",
            viewedItem: "Details 8",
            profilePic: clientPic3,
            lastViewed: "45s",
        },
        {
            id: 10,
            contactName: "Follow Up 9",
            details: "Alex Turner",
            viewedItem: "Details 9",
            profilePic: clientPic4,
            lastViewed: "50s",
        },
    ], []);

    return (<DataTableRecentlyActiveComponent data={data} columns={columns}/>);
}

export default RecentlyActiveComponent;
