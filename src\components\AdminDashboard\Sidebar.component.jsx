import { HiOutlineUsers } from "react-icons/hi";
import { BiMenuAltLeft, BiMenuAltRight } from "react-icons/bi";
import { PiOfficeChairBold, PiSquaresFourFill } from "react-icons/pi";
import { Button, Dropdown, Nav } from "react-bootstrap";
import { NavLink } from "react-router-dom";
import { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import useAuth from "../../redux/hooks/useAuth";
import "./sidebar.css";
import { ReactSVG } from "react-svg";
import dvConnectLogo from "../../assets/media/admin-logo.svg";
import adminService from "../../services/admin";
import { RiLogoutCircleLine } from "react-icons/ri";
import { GrUserNew } from "react-icons/gr";
import { AiOutlineUsergroupAdd } from "react-icons/ai";
import { FiUserCheck } from "react-icons/fi";
import shortLogo from "../../assets/media/Icons/short-logo.svg";
import { GoReport } from "react-icons/go";
import { Tooltip } from "react-tooltip";
import { GiHamburgerMenu } from "react-icons/gi";
import LanguageSwitcher from "../LanguageSwitcher";
import { MdOutlineSubscriptions, MdSupportAgent } from "react-icons/md";
import { useTranslation } from "react-i18next";
import {
  TbLayoutSidebarLeftCollapse,
  TbLayoutSidebarLeftExpand,
} from "react-icons/tb";
import useAdmin from "../../redux/hooks/useAdmin";

const SidebarComponent = ({
  setIsExpanded,
  iconSize,
  mobileSidebar,
  isExpanded,
}) => {
  const {
    todayClients,
    todayLeads,
    todayCompletedLeads,
    setTodayClients,
    setTodayCompletedLeads,
    setTodayLeads,
  } = useAdmin();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const userRole = user?.user?.role;
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === "ar";
  const menuRef = useRef(null);
  const [isHoverSupported, setIsHoverSupported] = useState(true);
  const [tooltipVisible, setTooltipVisible] = useState({}); // Manage which tooltip is visible
  const [visibleMenuItems, setVisibleMenuItems] = useState([]);
  const [hiddenMenuItems, setHiddenMenuItems] = useState([]);
  const { logout } = useAuth();

  const toggleSidebar = () => {
    setIsExpanded((prevState) => !prevState);
  };

  // Handle hover detection
  useEffect(() => {
    const mediaQuery = window.matchMedia("(hover: hover) and (pointer: fine)");
    setIsHoverSupported(mediaQuery.matches);

    const handleChange = () => setIsHoverSupported(mediaQuery.matches);
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  const handleTouchStart = useCallback((index) => {
    // Show the tooltip when the user touches the item
    setTooltipVisible((prevState) => ({ ...prevState, [index]: true }));

    // Hide the tooltip after 2 seconds (or any duration)
    setTimeout(() => {
      setTooltipVisible((prevState) => ({ ...prevState, [index]: false }));
    }, 2000); // 2 seconds delay
  }, []);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setIsExpanded(false);
      } else {
        setIsExpanded(true);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [setIsExpanded]);

  useEffect(() => {
    const fetchData = async () => {
      const [todayClients, todayLeads, todayCompletedLeads] = await Promise.all(
        [
          adminService.getTodayClients(),
          adminService.getTodayLeads(),
          adminService.getTodayCompletedLeads(),
        ]
      );
      setTodayClients(todayClients.data);
      setTodayLeads(todayLeads.data);
      setTodayCompletedLeads(todayCompletedLeads.data);
    };

    fetchData();
  }, [setTodayClients, setTodayLeads, setTodayCompletedLeads]);

  // Create menu items with memoization to prevent unnecessary recalculations
  const mainMenuItems = useMemo(
    () => [
      {
        id: 1,
        icon: <PiSquaresFourFill size={iconSize} />,
        label: t("sidebar.dashboard", "Dashboard"),
        link: "admin/dashboard",
        roles: [],
      },
      {
        id: 2,
        icon: <HiOutlineUsers size={iconSize} />,
        label: t("sidebar.clients", "Clients"),
        link: "admin/clients",
        roles: [],
      },
      {
        id: 3,
        icon: <PiOfficeChairBold size={iconSize} />,
        label: t("sidebar.team", "Team"),
        link: "admin/team",
        roles: [],
      },
      // {
      //     id: 4, icon: <GoReport size={iconSize}/>,
      //     label: t('sidebar.reports', "Reports"),
      //     link: "admin/reports",
      //     roles: []
      //     },
      {
        id: 5,
        icon: <MdOutlineSubscriptions size={iconSize} />,
        label: t("sidebar.subscriptions", "Subscriptions"),
        link: "admin/subscriptions/management",
        roles: ["Owner"],
      },
      {
        id: 8,
        icon: <MdSupportAgent size={iconSize} />,
        label: t("sidebar.support", "Support"),
        link: "admin/support",
        roles: ["Owner"],
      },
    ],
    [iconSize, t]
  );

  // Handle icons layout with memoized callback
  const handleIcons = useCallback(() => {
    if (!menuRef.current) return;
    const containerWidth = menuRef?.current?.offsetWidth;
    const itemWidth = 90; // Estimate the width of each item (icon + padding)
    const visibleItemsCount = Math.floor(containerWidth / itemWidth);

    const items = isRTL ? [...mainMenuItems].reverse() : mainMenuItems;

    setVisibleMenuItems(items.slice(0, visibleItemsCount));
    setHiddenMenuItems(items.slice(visibleItemsCount));
  }, [mainMenuItems, isRTL]);

  // Handle initial setup and resize
  useEffect(() => {
    // Add a small delay to ensure DOM is fully rendered
    const timer = setTimeout(() => {
      handleIcons();
    }, 100);

    window.addEventListener("resize", handleIcons);

    return () => {
      window.removeEventListener("resize", handleIcons);
      clearTimeout(timer);
    };
  }, [handleIcons]);

  // Handle language changes and mobile sidebar rendering
  useEffect(() => {
    handleIcons();
  }, [i18n.language, handleIcons, mobileSidebar]);

  // Force recalculation when mobile sidebar is active
  useEffect(() => {
    if (mobileSidebar && menuRef.current) {
      // Force recalculation on mobile sidebar mount
      const recalculateLayout = () => {
        const containerWidth = menuRef.current.offsetWidth;
        const itemWidth = 90;
        const visibleItemsCount = Math.floor(containerWidth / itemWidth);

        const items = isRTL ? [...mainMenuItems].reverse() : mainMenuItems;

        setVisibleMenuItems(items.slice(0, visibleItemsCount));
        setHiddenMenuItems(items.slice(visibleItemsCount));
      };

      // Run immediately and after a short delay to ensure proper rendering
      recalculateLayout();
      const timer = setTimeout(recalculateLayout, 300);

      return () => clearTimeout(timer);
    }
  }, [mobileSidebar, mainMenuItems, isRTL, handleIcons]);

  // Update Dropdown positioning based on RTL
  const dropdownProps = {
    drop: isRTL ? "up-right" : "up-left",
    align: isRTL ? "end" : "start",
  };

  return mobileSidebar ? (
    <div
      className={`mobileFooter admin-footer ${isRTL ? "rtl" : ""}`}
      ref={menuRef}
      onLoad={() => handleIcons()}
    >
      {visibleMenuItems.length > 0
        ? // Render visible items only if they exist
          visibleMenuItems.map((item) => (
            <div key={item?.id}>
              <Nav.Link
                as={NavLink}
                to={`/${item.link}`}
                className="menu-item-mobile"
                id={`navlink-${item?.id}`}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(item?.id)
                }
              >
                <div className="menu-icon-label-container">
                  <div className="icon-container">{item?.icon}</div>
                  <div className="label-container text-center">
                    {item?.label}
                  </div>
                </div>
              </Nav.Link>
              {!isExpanded && (
                <Tooltip
                  anchorSelect={`#navlink-${item?.id}`}
                  content={item?.label}
                  className="bg-dark text-white"
                  isOpen={
                    isHoverSupported ? undefined : tooltipVisible[item?.id]
                  }
                  place={isRTL ? "right" : "left"}
                />
              )}
            </div>
          ))
        : // Fallback if no visible items are calculated yet
          mainMenuItems.slice(0, 4).map((item) => (
            <div key={item?.id}>
              <Nav.Link
                as={NavLink}
                to={`/${item.link}`}
                className="menu-item-mobile"
                id={`navlink-${item?.id}`}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(item?.id)
                }
              >
                <div className="menu-icon-label-container">
                  <div className="icon-container">{item?.icon}</div>
                  <div className="label-container text-center">
                    {item?.label}
                  </div>
                </div>
              </Nav.Link>
            </div>
          ))}

      <Dropdown {...dropdownProps}>
        <Dropdown.Toggle id="dropdown-autoclose-true" variant="none">
          <GiHamburgerMenu size={iconSize} />
        </Dropdown.Toggle>

        <Dropdown.Menu
          className={`${isRTL ? "dropdown-menu-rtl" : ""} mobile-burger-menu`}
          style={{ maxHeight: "60vh", overflowY: "auto" }}
        >
          <Dropdown.Item>
            <div className={"text-center text-white"}>
              <div
                className={
                  "d-flex justify-content-between flex-column align-content-center"
                }
              >
                <div className={"sidebar-statistics"}>
                  {todayClients} <GrUserNew />
                </div>
                <div className={"sidebar-statistics"}>
                  {todayLeads} <AiOutlineUsergroupAdd />
                </div>
                <div className={"sidebar-statistics"}>
                  {todayCompletedLeads} <FiUserCheck />
                </div>
              </div>
            </div>
          </Dropdown.Item>
          <Dropdown.Item>
            <div className="menu-icon-label-container">
              <LanguageSwitcher />
            </div>
          </Dropdown.Item>
          {hiddenMenuItems.map((item) => (
            <Dropdown.Item
              key={item?.id}
              as={NavLink}
              to={`/${item.link}`}
              className="dropdown-item"
              id={`dropdown-navlink-${item?.id}`}
              onTouchStart={() =>
                !isHoverSupported && handleTouchStart(item?.id)
              }
            >
              <div className="menu-icon-label-container">
                <div>{item.icon}</div>
                <div className="label-container">{item?.label}</div>
              </div>
              {!isExpanded && (
                <Tooltip
                  anchorSelect={`#dropdown-navlink-${item?.id}`}
                  content={item.label}
                  className="bg-dark text-white"
                  isOpen={
                    isHoverSupported ? undefined : tooltipVisible[item?.id]
                  }
                  place={isRTL ? "right" : "left"}
                />
              )}
            </Dropdown.Item>
          ))}
        </Dropdown.Menu>
      </Dropdown>
    </div>
  ) : (
    <div
      className={`admin-sidebar ${isExpanded ? "expanded" : ""} ${
        isRTL ? "rtl" : ""
      }`}
    >
      <div className={"text-center sidebar-top-section"}>
        {isExpanded ? (
          <ReactSVG src={dvConnectLogo} />
        ) : (
          <ReactSVG src={shortLogo} />
        )}
      </div>
      <div className="admin-sidebar-content admin-sidebar-nav-container position-relative gap-3">
        <div className="mx-auto mt-3">
          {isExpanded ? (
            <TbLayoutSidebarLeftCollapse
              role={"button"}
              onClick={toggleSidebar}
              size={30}
              color="white"
            />
          ) : (
            <TbLayoutSidebarLeftExpand
              role={"button"}
              onClick={toggleSidebar}
              size={30}
              color="white"
            />
          )}
        </div>

        <div className={"text-uppercase text-white fs-6 text-center"}>
          {t("sidebar.todayReport", "today report")}
        </div>
        {isExpanded ? (
          <div className={"d-flex justify-content-evenly fs-6 text-center"}>
            <div>
              <span className={"fw-bold text-white"}>{todayClients}</span>
              <p style={{ color: "rgba(255, 255, 255, 0.5)" }}>
                {t("sidebar.newClients", "New Clients")}
              </p>
            </div>
            <div>
              <span className={"fw-bold text-white"}>{todayLeads}</span>
              <p style={{ color: "rgba(255, 255, 255, 0.5)" }}>
                {t("sidebar.newLeads", "New Leads")}
              </p>
            </div>
            <div>
              <span className={"fw-bold text-white"}>
                {todayCompletedLeads}
              </span>
              <p style={{ color: "rgba(255, 255, 255, 0.5)" }}>
                {t("sidebar.completedLeads", "Completed Leads")}
              </p>
            </div>
          </div>
        ) : (
          <div className={"text-center text-white"}>
            <div
              className={
                "d-flex justify-content-between flex-column align-content-center"
              }
            >
              <div className={"sidebar-statistics"}>
                {todayClients} <GrUserNew />
              </div>
              <div className={"sidebar-statistics"}>
                {todayLeads} <AiOutlineUsergroupAdd />
              </div>
              <div className={"sidebar-statistics"}>
                {todayCompletedLeads} <FiUserCheck />
              </div>
            </div>
          </div>
        )}

        <hr style={{ borderBottom: "#797979 solid 2px" }} />
        <Nav className="flex-column align-items-center" variant={"pills"}>
          {mainMenuItems.map(
            (item, index) =>
              (item?.roles?.includes(userRole) ||
                item?.roles?.length === 0) && (
                <Nav.Item key={index}>
                  <Nav.Link
                    as={NavLink}
                    to={`/${item.link}`}
                    className="menu-item my-2"
                    id={`navlink-sidebar-${index}`}
                  >
                    <div className="text-center"> {item.icon}</div>
                    <div className="menu-item_label ms-3">{item.label}</div>
                    {!isExpanded && (
                      <Tooltip
                        anchorSelect={`#navlink-sidebar-${index}`}
                        content={item.label}
                        className="bg-dark text-white"
                        place={isRTL ? "right" : "left"}
                      />
                    )}
                  </Nav.Link>
                </Nav.Item>
              )
          )}
          <Dropdown.Item>
            <div className="menu-icon-label-container">
              <LanguageSwitcher />
            </div>
          </Dropdown.Item>
        </Nav>
        <center>
          {isExpanded ? (
            <Button
              variant={"danger"}
              style={{ width: "fit-content" }}
              className={"px-5 mt-3"}
              onClick={() => logout()}
            >
              {t("sidebar.logout", "LogOut")}
            </Button>
          ) : (
            <Button
              variant={"danger"}
              style={{ width: "fit-content" }}
              className={"mt-3"}
              onClick={() => logout()}
            >
              <RiLogoutCircleLine />
            </Button>
          )}
        </center>
      </div>
    </div>
  );
};

export default SidebarComponent;
