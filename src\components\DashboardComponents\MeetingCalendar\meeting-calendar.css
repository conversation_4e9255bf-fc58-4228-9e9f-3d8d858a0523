.meeting-calendar-container {
    border-radius: 19px;
    background: #FFF;
    box-shadow: 0 4px 32px 0 rgba(103, 103, 103, 0.10);
    width: 100%;
    overflow: hidden;
    position: relative;
}

/* Calendar specific styles */
.calendar-container.rbc-calendar {
    width: 100%;
    height: 100vh;
    min-height: 700px;
    display: flex;
    flex-direction: column;
    @media screen and (max-width: 768px) {
        height: 100%;
    }
}

.calendar-container.rbc-calendar:has(.rbc-agenda-view) {
    height: fit-content;
    min-height: fit-content;
}

.calendar-container.rbc-calendar:has(.rbc-agenda-empty) {
    overflow: hidden;
}

.rbc-agenda-view{
    overflow: hidden;
}


/* Add horizontal scroll wrapper */
.rbc-calendar-wrapper {
    width: 100%;
    overflow-x: auto;
    overflow-y: visible; /* Changed from auto */
    -webkit-overflow-scrolling: touch;
    touch-action: pan-x pan-y; /* Allow both horizontal and vertical touch scrolling */
    position: relative;
    user-select: none;
}

/* Ensure the calendar container doesn't block vertical scrolling */
.calendar-container.rbc-calendar {
    touch-action: pan-x pan-y;
    height: auto; /* Changed from 100vh */
    min-height: 700px;
    max-height: 90vh; /* Add max-height to prevent excessive height */
    overflow: visible;
}

/* Ensure content is scrollable */
.rbc-time-view,
.rbc-month-view,
.rbc-agenda-view {
    min-width: 800px;
    width: 100%;
    touch-action: pan-x pan-y;
    overflow: visible;
}

/* Add active touch state */
.rbc-calendar-wrapper:active {
    cursor: grabbing;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .rbc-time-view,
    .rbc-month-view,
    .rbc-agenda-view {
        min-width: 700px;
        touch-action: pan-x pan-y;
    }

    .calendar-container.rbc-calendar {
        height: auto;
        min-height: 500px; /* Reduced minimum height for mobile */
        touch-action: pan-x pan-y;
    }
}

/* Prevent text selection during touch scroll */
.rbc-calendar * {
    user-select: none;
}

/* Month view specific */
.rbc-month-view {
    flex: 1;
    min-height: 600px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.rbc-month-row {
    flex: 1;
    min-height: 100px;
    display: flex;
}

.rbc-row-content {
    flex: 1;
}

.rbc-row-segment {
    padding: 2px 4px;
}

/* Month view cells */
.rbc-date-cell {
    padding: 4px;
    text-align: right;
}

.rbc-row-bg {
    display: flex;
    flex: 1;
}

.rbc-day-bg {
    flex: 1;
}

/* Event styles in month view */
.rbc-event {
    padding: 2px 5px !important;
    margin: 1px 5px !important;
    border-radius: 3px;
}

/* Responsive adjustments */
@media screen and (max-width: 1200px) {
    .rbc-time-view,
    .rbc-month-view,
    .rbc-agenda-view {
        min-width: 900px;
    }
}

@media screen and (max-width: 992px) {
    .rbc-time-view,
    .rbc-month-view,
    .rbc-agenda-view {
        min-width: 800px;
    }
}

@media screen and (max-width: 768px) {
    .rbc-time-view,
    .rbc-month-view,
    .rbc-agenda-view {
        min-width: 700px;
    }
}

/* Time slots in day/week view */
.rbc-time-content {
    min-height: 600px;
    overflow-x: hidden; /* Prevent double scrollbars */
}

.rbc-time-header {
    min-height: 75px;
}

/* Ensure consistent height for all time slots */
.rbc-timeslot-group {
    min-height: 60px !important;
    height: 60px !important; /* Force consistent height */
    flex: 1 1 60px !important; /* Ensure flex consistency */
}

.rbc-time-slot {
    min-height: 30px !important;
    height: 30px !important; /* Force consistent height */
    flex: 1 1 30px !important; /* Ensure flex consistency */
}

/* Ensure the last time slot doesn't collapse */
.rbc-time-content .rbc-time-gutter:last-child,
.rbc-time-content .rbc-day-slot:last-child {
    border-bottom: 1px solid #DDD;
}

/* Ensure consistent height for the time gutter */
.rbc-time-gutter .rbc-timeslot-group {
    min-height: 60px !important;
    height: 60px !important;
}

/* Add padding to the bottom of time content to prevent cutoff */
.rbc-time-content {
    padding-bottom: 1px; /* Prevent last slot from being cut off */
}

/* Ensure the time indicator aligns properly */
.rbc-current-time-indicator {
    height: 2px;
    z-index: 3;
    background-color: #74ad31;
}

/* Adjust responsive behavior */
@media screen and (max-width: 768px) {
    .rbc-timeslot-group {
        min-height: 50px !important;
        height: 50px !important;
    }

    .rbc-time-slot {
        min-height: 25px !important;
        height: 25px !important;
    }
}

/* Day/Week view columns */
.rbc-day-slot {
    min-width: 150px !important;
}

.rbc-time-column {
    min-width: 100px !important;
}

/* Time gutter (left side time indicators) */
.rbc-time-gutter {
    min-width: 100px !important;
}

/* Header cells */
.rbc-header {
    min-height: 50px;
    padding: 8px !important;
}

/* Event styles */
.rbc-event {
    min-height: 30px !important;
    padding: 5px !important;
}

.rbc-day-slot .rbc-event {
    min-height: 30px !important;
}

/* Toolbar styles */
.rbc-toolbar {
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
    padding: 10px;
    margin-bottom: 10px !important;
}

.rbc-toolbar-label {
    width: 100%;
    text-align: center;
    margin: 5px 0;
    font-size: 1.2em;
    font-weight: bold;
}

.rbc-btn-group {
    margin: 5px !important;
}

/* Scrollbar styles */
.rbc-calendar-wrapper::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.rbc-calendar-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.rbc-calendar-wrapper::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.rbc-calendar-wrapper::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* RTL Support */
[dir="rtl"] .rbc-btn-group > button:first-child:not(:last-child) {
    border-radius: 0 4px 4px 0;
}

[dir="rtl"] .rbc-btn-group > button:last-child:not(:first-child) {
    border-radius: 4px 0 0 4px;
}
