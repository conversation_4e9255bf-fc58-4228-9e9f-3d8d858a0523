.pricing-header {
    border-radius: 19px;
    background: url(../../assets/media/subscriptionBG.png) no-repeat center;
    background-size: cover;
    min-height: 230px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
}

.pricing-card {
    border-radius: 16px;
    background: #FFF;
    box-shadow: 17px 18px 20px 0 rgba(0, 0, 0, 0.00);
    height: fit-content;
}

.pricing-card1-collapse {
    border-radius: 0 0 16px 16px;
    background: #145CBA;
}

.outline-card1-btn {
    color: #145CBA;
    font-weight: 500;
    border-radius: 23px;
    background: #FFF;
    text-transform: uppercase;
    border: unset;
    padding: 11px 14px;
    transition: color, background-color 0.25s ease-in-out;
}

.outline-card1-btn:hover {
    background-color: #124587;
    color: #FFF;
}

.pricing-card2-collapse {
    border-radius: 0 0 16px 16px;
    background: #92C020;
}

.outline-card2-btn {
    color: #92C020;
    font-weight: 500;
    border-radius: 23px;
    background: #FFF;
    text-transform: uppercase;
    border: unset;
    padding: 11px 14px;
    transition: color, background-color 0.25s ease-in-out;
}

.outline-card2-btn:hover {
    background-color: #80AA17;
    color: #FFF;
}

.pricing-card3-collapse {
    border-radius: 0 0 16px 16px;
    background: #E4AB15;
}

.outline-card3-btn {
    color: #E4AB15;
    font-weight: 500;
    border-radius: 23px;
    background: #FFF;
    text-transform: uppercase;
    border: unset;
    padding: 11px 14px;
    transition: color, background-color 0.25s ease-in-out;
}

.outline-card3-btn:hover {
    background-color: #bf8900;
    color: #FFF;
}

.payment-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    text-align: center;
    background-color: #f0f8ff;
}

.payment-success h1 {
    font-size: 2rem;
    color: #4CAF50;
}

.payment-success p {
    font-size: 1.2rem;
    color: #555;
}

.checkmark-icon {
    color: transparent;
    background: linear-gradient(#92C020, #CAD511);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
}

.radial-gradient-border-static {
    position: relative;
    padding: 20px;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

div.radial-gradient-border-static p {
    font-weight: 700;
}

.radial-gradient-border-static::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    background: linear-gradient(to top left, #92C020, #CAD511);
    padding: 3px; /* Border width */
    border-radius: 12px;
}

.radial-gradient-border-static::after {
    content: "";
    position: absolute;
    top: 3px; /* Same as border width */
    right: 3px; /* Same as border width */
    bottom: 3px; /* Same as border width */
    left: 3px; /* Same as border width */
    z-index: -1;
    background: white;
    border-radius: 9px;
}

/* Add specific styling for the table */
table.radial-gradient-border-static {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

table.radial-gradient-border-static th {
    font-weight: 600;
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

table.radial-gradient-border-static td {
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
}

table.radial-gradient-border-static tr:last-child td {
    border-bottom: none;
}

.discount-badge {
    display: inline-flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 4px 8px;
}

.original-price {
    text-decoration: line-through;
    color: #6c757d;
    margin-right: 8px;
    font-size: 0.9rem;
}

.discount-text {
    color: #92C020;
    font-weight: bold;
    font-size: 0.9rem;
}

.payment-failed {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    text-align: center;
    background-color: #ffe6e6;
}

.payment-failed h1 {
    font-size: 2rem;
    color: #e74c3c;
}

.payment-failed p {
    font-size: 1.2rem;
    color: #555;
}

.date-time-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-time-container .time {
  font-size: 0.85em;
}

/* Add RTL support for table headers */
[dir="rtl"] .packages-table th {
    text-align: right !important;
}

[dir="rtl"] .table.packages-table > :not(caption) > * > * {
    text-align: right;
}

/* Adjust sort icons in RTL */
[dir="rtl"] .table.packages-table th span {
    margin-right: 0.5rem;
    margin-left: 0;
    display: inline-block;
}