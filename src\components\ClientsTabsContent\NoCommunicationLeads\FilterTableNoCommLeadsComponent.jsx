import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ReactSVG } from "react-svg";
import { format } from "date-fns";
import { MdAssignmentTurnedIn, MdEdit } from "react-icons/md";
import { Tooltip } from "react-tooltip";
import { PiTrashFill } from "react-icons/pi";
import { FaCalendar, FaEnvelope, FaPhone, FaUserPlus } from "react-icons/fa6";
import { FaUserCog } from "react-icons/fa";
import { Dropdown } from "react-bootstrap";
import { Link } from "react-router-dom";
import DataTableComponent from "../../CustomDataTable/DataTable.component";
import { sourceToIcon } from "../../../constants/sourceIcons";
import getAllTeamMembers from "../../../services/teams/get-teams.api";
import CenteredModal from "../../Shared/modals/CenteredModal/CenteredModal";
import DeleteLeadFromTable from "../../Modals/DeleteLeadFromTable";
import {
  setTeamMembers,
  setDisableAddMember,
  handleAssignTeamMemberThunk,
} from "../../../redux/features/clientSlice";
import { showErrorToast } from "../../../utils/toast-success-error";
import { useTranslation } from "react-i18next";
import { IoSparklesSharp } from "react-icons/io5";

const FilterTableNoCommLeadsComponent = ({ loading, data, hideNoData }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );
  const { currentUserPermissions } = useSelector((state) => state.auth);

  const [teamMembers, setLocalTeamMembers] = useState([]);
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState(null);

  // Tooltip helpers
  const [isHoverSupported, setIsHoverSupported] = useState(true);
  const [tooltipVisible, setTooltipVisible] = useState({});

  const handleTouchStart = (index) => {
    setTooltipVisible((prevState) => ({ ...prevState, [index]: true }));
    setTimeout(() => {
      setTooltipVisible((prevState) => ({ ...prevState, [index]: false }));
    }, 2000);
  };

  const handleDelete = (id) => {
    setSelectedLeadId(id);
    setShowCenteredModal(true);
  };

  // Fetch team members once
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const teamsData = await getAllTeamMembers();
        const members = teamsData?.data?.members || [];
        setLocalTeamMembers(members);
        dispatch(setTeamMembers(members));
        dispatch(setDisableAddMember(teamsData?.data?.quota === 0));
      } catch (error) {
        showErrorToast(
          error.response?.data?.message || t("messages.fetchTeamError")
        );
      }
    };
    fetchTeamMembers();

    const mediaQuery = window.matchMedia("(hover: hover) and (pointer: fine)");
    setIsHoverSupported(mediaQuery.matches);
    const handleChange = () => setIsHoverSupported(mediaQuery.matches);
    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [dispatch, t]);

  // Data transformation
  const tableData = useMemo(() => {
    return (Array.isArray(data) ? data : []).map((lead, index) => ({
      id: lead?.id,
      clientId: index + 1 + (currentPage - 1) * recordsPerPage,
      contactName: lead?.name,
      email: lead?.email,
      phone: lead?.phone,
      source: lead?.source,
      service: lead?.service,
      pageName: lead?.page_name,
      logs: lead?.latestActivity || lead?.activities,
      assignedTo: lead?.assignedTo || lead?.assigned_to?.name,
      createdAt: lead?.date || lead?.created_at,
      updatedAt: lead?.updated_at,
    }));
  }, [data, currentPage, recordsPerPage]);

  const filteredTeamMembers = useMemo(
    () => teamMembers?.filter((member) => member.status !== "deactive"),
    [teamMembers]
  );

  const columns = useMemo(
    () => [
      // Hidden ID column (clientId) no longer displayed
      {
        id: "clientId",
        accessor: "clientId",
        Header: "ID",
        Cell: () => null,
      },
      // Hidden updatedAt column for sorting
      {
        id: "updatedAt",
        accessor: "updatedAt",
        Header: "UpdatedAt",
        Cell: () => null,
      },
      {
        Header: t("leadsTable.columns.contactName"),
        accessor: "contactName",
        Cell: ({ row }) => {
          const name = row.original.contactName;
          return (
            <>
              <div
                className={`d-flex align-items-center gap-1 lead-name${row.original.id}`}
                style={{ maxWidth: "200px" }}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(row.original.id)
                }
              >
                <span className="one-line">{name}</span>
              </div>
              <Tooltip
                anchorSelect={`.lead-name${row.original.id}`}
                content={name}
                className="bg-dark text-white"
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
              />
            </>
          );
        },
      },
      {
        Header: t("leadsTable.columns.phone"),
        accessor: "phone",
      },
      {
        Header: t("leadsTable.columns.assignedTo"),
        accessor: "assignedTo",
        Cell: ({ row }) => {
          const assignedTo = row.original.assignedTo;
          return assignedTo ? (
            assignedTo
          ) : (
            <Dropdown>
              <Dropdown.Toggle
                variant="light"
                id="dropdown-basic"
                className={"team-actions-button p-0 rounded-3"}
              >
                <div className={"assign-client-icon m-0"}>
                  <FaUserPlus size={20} />
                </div>
              </Dropdown.Toggle>
              <Dropdown.Menu className={"team-actions-menu"} container="body">
                {filteredTeamMembers?.length > 0 ? (
                  filteredTeamMembers?.map((member) => (
                    <Dropdown.Item
                      key={member.id}
                      className={
                        "d-flex justify-content-between align-items-center text-secondary"
                      }
                      onClick={() =>
                        dispatch(
                          handleAssignTeamMemberThunk({
                            leadId: row.original.id,
                            memberId: member.id,
                          })
                        )
                      }
                    >
                      {member.name}
                    </Dropdown.Item>
                  ))
                ) : (
                  <Dropdown.Item disabled>
                    {t("leadsTable.columns.noTeamMembers")}
                  </Dropdown.Item>
                )}
              </Dropdown.Menu>
            </Dropdown>
          );
        },
      },
      {
        Header: t("leadsTable.columns.reassign"),
        accessor: "reassign",
        Cell: ({ row }) => {
          const assignedTo = row.original.assignedTo;

          if (!assignedTo) {
            return "-";
          }

          if (!currentUserPermissions?.includes("lead-edit")) {
            return assignedTo;
          }

          const availableMembers = filteredTeamMembers?.filter(
            (m) => m.name !== assignedTo
          );

          return (
            <Dropdown>
              <Dropdown.Toggle
                variant="light"
                id="reassign-dropdown"
                className={"team-actions-button p-0 rounded-3"}
              >
                <div className={"assign-client-icon m-0"}>
                  <FaUserCog size={20} />
                </div>
              </Dropdown.Toggle>
              <Dropdown.Menu className={"team-actions-menu"} container="body">
                {availableMembers && availableMembers.length > 0 ? (
                  availableMembers.map((member) => (
                    <Dropdown.Item
                      key={member.id}
                      className={
                        "d-flex justify-content-between align-items-center text-secondary"
                      }
                      onClick={() =>
                        dispatch(
                          handleAssignTeamMemberThunk({
                            leadId: row.original.id,
                            memberId: member.id,
                          })
                        )
                      }
                    >
                      {member.name}
                    </Dropdown.Item>
                  ))
                ) : (
                  <Dropdown.Item disabled>
                    {t("leadsTable.columns.noTeamMembers")}
                  </Dropdown.Item>
                )}
              </Dropdown.Menu>
            </Dropdown>
          );
        },
      },
      {
        Header: t("leadsTable.columns.source"),
        accessor: "source",
        Cell: ({ row }) => {
          const src = row.original.source;
          const IconComponent = sourceToIcon[src];
          return src ? (
            <div className={"mx-auto social-icon-container"}>
              {IconComponent && <ReactSVG src={IconComponent} />}
            </div>
          ) : (
            "-"
          );
        },
      },
      {
        Header: t("leadsTable.columns.service"),
        accessor: "service",
      },
      {
        Header: t("leadsTable.columns.pageName"),
        accessor: "pageName",
        Cell: ({ row }) => {
          const pageName = row.original.pageName;
          return (
            <>
              <div
                className={`one-line page-name${row.original.id} mx-auto`}
                style={{ maxWidth: "150px" }}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(row.original.id)
                }
              >
                {pageName}
              </div>
              <Tooltip
                anchorSelect={`.page-name${row.original.id}`}
                content={pageName}
                className={"bg-dark text-white"}
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
              />
            </>
          );
        },
      },
      {
        Header: t("leadsTable.columns.lastActivity"),
        accessor: "lastActivity",
        Cell: ({ row }) => {
          const tooltipId = `lastActivity_${row.id}`;
          const logs = Array.isArray(row.original.logs)
            ? row.original.logs.slice(-3)
            : [row.original.logs];
          const lastLog = logs[0];
          if (!lastLog) return null;
          return (
            <Link
              to={`/leads/${row.original.id}`}
              data-for={tooltipId}
              data-tooltip-id={tooltipId}
              onTouchStart={() =>
                !isHoverSupported && handleTouchStart(row.original.id)
              }
            >
              <div className={"activity-button"}>
                {lastLog?.action === 1 && <MdAssignmentTurnedIn size={20} />}
                {lastLog?.action === 2 && <FaPhone size={20} />}
                {lastLog?.action === 3 && <FaCalendar size={20} />}
                {lastLog?.action === 4 && <FaEnvelope size={20} />}
              </div>
              <Tooltip
                id={tooltipId}
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
                className={"logs-tooltip-container"}
                content={
                  <div className={"logs-list-container"}>
                    {logs?.map((log, index) => (
                      <div key={index} className={"log-container"}>
                        <div className={"d-flex justify-content-center my-2"}>
                          {log?.action === 1 && (
                            <MdAssignmentTurnedIn
                              size={20}
                              className={"mainColor"}
                            />
                          )}
                          {log?.action === 2 && (
                            <FaPhone size={20} className={"mainColor"} />
                          )}
                          {log?.action === 3 && (
                            <FaCalendar size={20} className={"mainColor"} />
                          )}
                          {log?.action === 4 && (
                            <FaEnvelope size={20} className={"mainColor"} />
                          )}
                        </div>
                        <p className={"opacity-50"}>
                          {log?.result || log?.note}
                        </p>
                      </div>
                    ))}
                  </div>
                }
                place={"left-start"}
                events={["hover"]}
              />
            </Link>
          );
        },
      },
      {
        Header: t("leadsTable.columns.createdAt"),
        accessor: "createdAt",
        Cell: ({ value, row }) => {
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              const formattedDate = format(parsedDate, "yyyy-MM-dd HH:mm:ss");
              return (
                <Link to={`/leads/${row.original.id}`}>{formattedDate}</Link>
              );
            }
          }
          return <Link to={`/leads/${row.original.id}`}>{value}</Link>;
        },
      },
      {
        Header: t("leadsTable.columns.actions"),
        Cell: ({ row }) => (
          <div className={"d-flex justify-content-center"}>
            {currentUserPermissions?.includes("lead-edit") && (
              <Link
                to={`/leads/${row.original.id}`}
                className={"me-3 shadow-sm rounded-2 p-1"}
              >
                <MdEdit size={20} className={"text-dark"} />
              </Link>
            )}
            {currentUserPermissions?.includes("lead-delete") && (
              <div className={"shadow-sm rounded-2 p-1"}>
                <PiTrashFill
                  onClick={() => handleDelete(row.original.id)}
                  size={20}
                  className={"text-danger"}
                />
              </div>
            )}
          </div>
        ),
      },
    ],
    [
      teamMembers,
      t,
      filteredTeamMembers,
      isHoverSupported,
      tooltipVisible,
      currentUserPermissions,
      dispatch,
    ]
  );

  const initialSortBy = useMemo(() => [{ id: "updatedAt", desc: true }], []);
  const hiddenColumns = useMemo(() => ["clientId", "updatedAt"], []);

  return (
    <>
      <DataTableComponent
        columns={columns}
        data={tableData}
        initialSortBy={initialSortBy}
        hiddenColumns={hiddenColumns}
        loading={loading}
        hideNoData={hideNoData}
      />
      <CenteredModal
        show={showCenteredModal}
        children={
          <DeleteLeadFromTable
            leadId={selectedLeadId}
            setShowCenteredModal={setShowCenteredModal}
          />
        }
        onHide={() => {
          setShowCenteredModal(false);
          setSelectedLeadId(null);
        }}
      />
    </>
  );
};

export default FilterTableNoCommLeadsComponent;
