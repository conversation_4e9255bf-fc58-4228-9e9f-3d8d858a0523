import "./Clients.css";
import FilterTableLeadsComponent from "./FilterTableLeadsComponent";
import PaginationComponent from "../../CustomDataTable/PaginationComponent";
import TableControllers from "../../CustomDataTable/TableControllers";
import { useCallback, useState } from "react";
import leadService from "../../../services/leads";
import { Container } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import CenteredModal from "../../Shared/modals/CenteredModal/CenteredModal";
import FacebookTokenExpiredModal from "./FacebookTokenExpiredModal";
import {
  handleModalClose,
  showModal,
} from "../../../redux/features/facebookTokenModalSlice";
import { showErrorToast } from "../../../utils/toast-success-error";
import {
  setTotalPages,
  setCurrentPage,
} from "../../../redux/features/leadsPaginationSlice";
import useClient from "../../../redux/hooks/useClient";

const AllLeadsComponent = () => {
  const {
    filterStatus,
    selectedSource,
    setLeads,
    setLeadStatusCounts,
    setFilterStatus,
    setSelectedSource,
  } = useClient();
  const dispatch = useDispatch();
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );
  const { showTokenModal, hasShownModal } = useSelector(
    (state) => state.facebookTokenModal
  );
  const [abortController, setAbortController] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);

  // Create a combined filter function that can be used by both components
  const applyFilters = useCallback(
    async (status, source) => {
      try {
        setLoading(true);
        // Cancel any ongoing request before starting a new one
        if (abortController) abortController.abort();

        const controller = new AbortController();
        setAbortController(controller);

        // Reset search state when filtering
        setIsSearchActive(false);

        if (status === "all" && !source) {
          // If no filters are active, fetch all data
          await fetchData(controller);
        } else {
          // Apply filters
          const response = await leadService.filterLeadsByStatusApi({
            source: source ? source.toString() : null,
            status: status !== "all" ? status.toString() : null,
            records: recordsPerPage,
            currentPage,
            controller: controller,
          });

          if (response?.data && response.data["All Leads"]) {
            setLeads(response.data["All Leads"]);
            dispatch(setTotalPages(response.data["Number Of Pages"] || 1));
            dispatch(setCurrentPage(response.data["Page Number"] || 1));
          } else {
            setLeads(response?.data);
          }
        }
      } catch (error) {
        console.error("Error applying filters:", error);
        showErrorToast(error.response?.data?.message || "An error occurred");
      } finally {
        setLoading(false);
      }
    },
    [
      abortController,
      currentPage,
      recordsPerPage,
      dispatch,
      setIsSearchActive,
      setLoading,
    ]
  );

  // Add the handleSourceFilter function
  const handleSourceFilter = useCallback(
    async (source) => {
      // Use the combined filter function
      await applyFilters(filterStatus, source);
    },
    [applyFilters, filterStatus]
  );

  const handleFilterLeads = async (term, controller) => {
    setLoading(true);

    try {
      // Cancel any ongoing request before starting a new one
      if (abortController) {
        abortController.abort();
      }

      const newController = controller || new AbortController();
      setAbortController(newController);

      // If search term is empty, reset search state and fetch all data
      if (!term || term.length === 0) {
        setIsSearchActive(false);
        // Reset filters
        setFilterStatus("all");
        setSelectedSource(null);
        await fetchData(newController);
        return;
      }

      // Set search as active to hide pagination
      setIsSearchActive(true);

      // Reset filters when searching
      setFilterStatus("all");
      setSelectedSource(null);

      const leadsData = await leadService.filterLeadsApi(
        { status: term },
        newController.signal // Pass the signal to the API request
      );

      // Only update state if the request wasn't aborted
      if (!newController.signal.aborted) {
        if (leadsData?.data && leadsData.data["All Leads"]) {
          setLeads(leadsData.data["All Leads"]);
          dispatch(setTotalPages(leadsData.data["Number Of Pages"] || 1));
          dispatch(setCurrentPage(leadsData.data["Page Number"] || 1));
        } else {
          setLeads(leadsData?.data);
        }
      }
    } catch (error) {
      // Only log errors that aren't from aborted requests
      if (error.name !== "AbortError") {
        console.error("Error fetching leads:", error);
      }
    } finally {
      // Only update loading state if the request wasn't aborted
      if (!controller?.signal.aborted) {
        setLoading(false);
      }
    }
  };

  // Filter leads based on status when handleFilterStatus is triggered
  const handleFilterStatus = useCallback(
    async (status) => {
      // Use the combined filter function
      await applyFilters(status, selectedSource);
    },
    [applyFilters, selectedSource]
  );

  const fetchData = async (controller) => {
    try {
      setLoading(true);
      setIsSearchActive(false);

      const leadsData = await leadService.getAllLeadsApi(
        controller,
        recordsPerPage,
        currentPage
      );

      if (leadsData?.data && leadsData.data["All Leads"]) {
        const { completed, inprogress, pendding, rejected, assigned } =
          leadsData?.data;
        setLeads(leadsData.data["All Leads"]);
        dispatch(setTotalPages(leadsData.data["Number Of Pages"] || 1));
        dispatch(setCurrentPage(leadsData.data["Page Number"] || 1));
        setLeadStatusCounts({
          completed: completed || 0,
          assigned: assigned || 0,
          inprogress: inprogress || 0,
          pendding: pendding || 0,
          rejected: rejected || 0,
        });
      } else {
        setLeads(leadsData?.data);
      }

      if (
        !hasShownModal &&
        leadsData?.data?.access_token_status === false &&
        user?.user["access-token"]
      ) {
        dispatch(showModal());
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      showErrorToast(error.response?.data?.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };
  const { user } = useSelector((state) => state.auth);
  const { t } = useTranslation();
  // Remove the useEffect that was resetting hasCheckedToken
  return (
    <Container className={"all-leads-table p-4"}>
      {user?.user?.package_id === 1 && (
        <div className="quota-container">
          <div className="radial-gradient-border">
            {t("leadsTable.quotaExceeded")}
          </div>
          <Link to={"/packages"}>
            <button className="submit-btn">{t("common.upgradeNow")}</button>{" "}
          </Link>
        </div>
      )}
      <TableControllers
        setLoading={setLoading}
        abortController={abortController}
        fetchData={fetchData}
        handleFilterStatus={handleFilterStatus}
        handleFilterLeads={handleFilterLeads}
        handleSourceFilter={handleSourceFilter}
        setAbortController={setAbortController}
        isSearchActive={isSearchActive}
      />
      <FilterTableLeadsComponent
        handleFilterLeads={handleFilterLeads}
        loading={loading}
        abortController={abortController}
        setAbortController={setAbortController}
        setLoading={setLoading}
        handleFilterStatus={handleFilterStatus}
        isSearchActive={isSearchActive}
        applyFilters={applyFilters} // Pass the new function
      />
      {!isSearchActive && <PaginationComponent />}
      <CenteredModal
        show={showTokenModal}
        onHide={() => dispatch(handleModalClose())}
      >
        <FacebookTokenExpiredModal
          onHide={() => dispatch(handleModalClose())}
        />
      </CenteredModal>
    </Container>
  );
};

export default AllLeadsComponent;

// Replace setTotalPages(x) with dispatch(setTotalPages(x))
// Replace setCurrentPage(x) with dispatch(setCurrentPage(x))
// Replace currentPage and recordsPerPage usages with values from Redux
