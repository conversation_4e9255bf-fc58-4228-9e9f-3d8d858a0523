.members-status-switch .form-check-input {
    min-height: 25px;
    min-width: 50px;
    background-color: #E35757;
    color: white;
}

.members-status-switch.form-switch .form-check-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%27-4 -4 8 8%27%3e%3ccircle r=%273%27 fill=%27%23fff%27/%3e%3c/svg%3e");
}

.members-status-switch .form-check-input:checked {
    background-color: #92C020;
    border-color: #92C020;
}

.members-status-switch .form-switch .form-check-input {
    margin-top: 0;
}

.team-actions-button:after {
    content: unset;
}

.team-actions-menu {
    border-radius: 12px;
    background: #FFF;
    box-shadow: 0 4px 24px -7px rgba(0, 0, 0, 0.20);
    min-width: 195px;
    max-height: 200px;
    overflow-y: scroll;
}

.new-member-field .form-control, .new-member-field input, .new-member-field select {
    border-radius: 14px;
    border: 1px solid #92C020;
    background: #FFF;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.10);
}

.new-member-field .PhoneInputInput {
    padding-top: 6px;
    padding-bottom: 6px;
    padding-left: 55px;
}

.new-member-field .PhoneInputCountry{
    position: absolute;
    top: 50%;
    left: 8%;
    transform: translate(-50%, -50%);
    z-index: 99;
}

[dir="rtl"] .new-member-field input::placeholder {
    text-align: right;
}

.member-modal-close-icon {
    position: absolute;
    top: 50%;
    right: -5%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    @media (max-width: 768px) {
        right: -15%;
    }
}

.member-modal-permission {
    border-radius: 11px;
    background: #FFF;
    box-shadow: 0 0 38px -4px rgba(0, 0, 0, 0.10);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 1.5rem 2rem;
}

.edit-content-icon {
    background-color: #92C020;
    color: white;
    border-radius: 50%;
    padding: 8px;
}

.edit-group-icon {
    background-color: white;
    color: #92C020;
    border-radius: 50%;
    padding: 8px;
}

.member-modal-permission-edit {
    border-radius: 11px;
    background: #FFF;
    box-shadow: 0 0 38px -4px rgba(0, 0, 0, 0.10);
    padding: 1.5rem 2rem;
}

.edit-per-switch {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
    padding-left: 0;
}

.unassigned-clients-checkbox .form-check-input {
    border-radius: 4px;
    border: 1px solid #BDD015;
    width: 23px;
    height: 23px;
    margin: 0 12px;
}

.unassigned-clients-checkbox .form-check-input:checked {
    background-color: #BDD015;
}

.overview-leadAssignment {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    padding: 1rem;
}

.assignment-rule-card {
    border-radius: 22px;
    background: #FFF;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.15);
}

.assignment-rule-table {
    border-radius: 22px;
    background: #FFF;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.15);
}

.assignment-rule-card.selected-card {
    background-color: #FCFFF3;
    border: 1px solid #94C11F;
    transition: all ease-in-out 0.25s;
}

.assignment-rule-card.selected-card .rule-title {
    color: #92C020;
    transition: color ease-in-out 0.25s;
}

.teamMemberProfileContainer {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    /*box-shadow: 0px 4px 60px -7px rgba(0, 0, 0, 0.10);*/
    padding-top: 120px;
    margin-top: -80px;
}

.TM-statistics {
    border-radius: 12px;
    background: #FFF;
    box-shadow: 0 4px 53px -7px rgba(0, 0, 0, 0.10);
    max-width: 200px;
    padding-top: 10px;
}

.TM-info-container {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    position: relative;
    z-index: 98;
    padding: 20px;
}

.TM-chart-container {
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0 3px 45px -5.973px rgba(0, 0, 0, 0.10);
}

.label-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

td:has(a.team-name-cell) {
    width: 300px;
    max-width: 300px;
}