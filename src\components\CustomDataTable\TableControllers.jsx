import { use<PERSON>allback, useState } from "react";
import {
  <PERSON>ton,
  ButtonGroup,
  Col,
  Row,
  OverlayTrigger,
  Tooltip as BSTooltip,
} from "react-bootstrap";
import SearchComponent from "./SearchComponent";
import { IoReturnUpBackOutline } from "react-icons/io5";
import { ReactSVG } from "react-svg";
import { FaFilterCircleXmark, FaUserClock, FaUserPlus } from "react-icons/fa6";
import { MdAssignmentTurnedIn, MdPendingActions } from "react-icons/md";
import { BsGearFill } from "react-icons/bs";
import { LuImport } from "react-icons/lu";
import { PiExportBold } from "react-icons/pi";
import { Tooltip } from "react-tooltip";
import CreateClientModal from "../Modals/CreateClientModal";
import { MdDoneAll, MdCancel } from "react-icons/md";
import { useSelector, useDispatch } from "react-redux";
import ImportLeadsDropZone from "../Modals/ImportLeadsModal";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import { useTranslation } from "react-i18next";
import "./StatusButtons.css";
import { orderedSourceKeys, sourceToIcon } from "../../constants/sourceIcons";
import useClient from "../../redux/hooks/useClient";

const TableControllers = ({
  abortController,
  setAbortController,
  fetchData,
  handleFilterLeads,
  handleFilterStatus,
  setLoading,
  handleSourceFilter,
  minimal = false,
  nocommunication = false,
  onClearAllFilters,
  loading = false,
}) => {
  // Fetch pagination data from dedicated slice to avoid redundant re-renders and API calls
  const {
    filterStatus,
    leadStatusCounts,
    selectedSource,
    setFilterStatus,
    setSelectedSource,
    handleExportLeadsThunk,
    handleImportLeadsThunk,
    filterLeadsBySource,
  } = useClient();

  // Keep pagination state source-of-truth in leadsPagination slice
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );

  const [showImportLeadModal, setShowImportLeadModal] = useState(false);
  const { currentUserPermissions } = useSelector((state) => state.auth);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const handleClose = () => setShowCreateModal(false);
  const handleShow = () => setShowCreateModal(true);
  const { t } = useTranslation();

  const handleImportLeads = (data) => {
    handleImportLeadsThunk(data);
  };

  // Status options for NoCommunicationLeads
  const noCommStatusOptions = [
    {
      key: "all",
      label: t("leadsTable.columns.all"),
      value: "all",
      color: "#6c757d",
      tooltip: t("leadsTable.columns.all"),
    },
    {
      key: "inprogress",
      label: <BsGearFill size={20} />,
      value: 1,
      color: "#007bff",
      count: leadStatusCounts?.inprogress ?? 0,
      tooltip: t("leadsTable.columns.inprogress"),
    },
    {
      key: "rejected",
      label: <MdCancel size={20} />,
      value: 3,
      color: "#dc3545",
      count: leadStatusCounts?.rejected ?? 0,
      tooltip: t("leadsTable.columns.rejected"),
    },
    {
      key: "wrong_lead",
      label: <span style={{ fontWeight: 600 }}>WL</span>,
      value: 4,
      color: "#b8860b",
      count: leadStatusCounts?.wrong_lead ?? 0,
      tooltip: t("leadsTable.columns.wrong_lead"),
    },
    {
      key: "not_qualified",
      label: <span style={{ fontWeight: 600 }}>NQ</span>,
      value: 5,
      color: "#6f42c1",
      count: leadStatusCounts?.not_qualified ?? 0,
      tooltip: t("leadsTable.columns.not_qualified"),
    },
    {
      key: "no_communication",
      label: <span style={{ fontWeight: 600 }}>NC</span>,
      value: 6,
      color: "#17a2b8",
      count: leadStatusCounts?.no_communication ?? 0,
      tooltip: t("leadsTable.columns.no_communication"),
    },
    {
      key: "canceled",
      label: <MdCancel size={20} style={{ transform: "rotate(45deg)" }} />,
      value: 9,
      color: "#adb5bd",
      count: leadStatusCounts?.canceled ?? 0,
      tooltip: t("leadsTable.columns.canceled"),
    },
    {
      key: "assigned",
      label: <MdAssignmentTurnedIn size={20} />,
      value: 11,
      color: "#6610f2",
      count: leadStatusCounts?.assigned ?? 0,
      tooltip: t("leadsTable.columns.assigned"),
    },
  ];

  const statusOptions = nocommunication
    ? noCommStatusOptions
    : [
        {
          key: "all",
          label: t("leadsTable.columns.all"),
          value: "all",
          color: "#6c757d",
        },
        {
          key: "pending",
          label: <MdPendingActions size={20} />,
          value: 0,
          color: "#ffc107",
          count: leadStatusCounts?.pendding ?? 0,
        },
        {
          key: "assigned",
          label: <MdAssignmentTurnedIn size={20} />,
          value: 11,
          color: "#6610f2",
          count: leadStatusCounts?.assigned ?? 0,
        },
        {
          key: "inProgress",
          label: <BsGearFill size={20} />,
          value: 1,
          color: "#007bff",
          count: leadStatusCounts?.inprogress ?? 0,
        },
        {
          key: "completed",
          label: <MdDoneAll size={20} />,
          value: 2,
          color: "#28a745",
          count: leadStatusCounts?.completed ?? 0,
        },
        {
          key: "rejected",
          label: <MdCancel size={20} />,
          value: 3,
          color: "#dc3545",
          count: leadStatusCounts?.rejected ?? 0,
        },
      ];

  // Add a state to track the search input value
  const [searchValue, setSearchValue] = useState("");

  const handleClearFilters = () => {
    setSelectedSource(null);
    setFilterStatus("all");
    setSearchValue(""); // Reset search value

    // For no communication component, use the onClearAllFilters callback
    // to avoid duplicate API calls
    if (typeof onClearAllFilters === "function") {
      onClearAllFilters();
    } else {
      // Only call fetchData directly if onClearAllFilters is not provided
      if (abortController) abortController.abort();
      const controller = new AbortController();
      setAbortController(controller);
      fetchData(controller);
    }
  };

  const handleSearch = useCallback(
    async (term, controller, isCancellation) => {
      if (term !== "" && !isCancellation) {
        // Return the promise from handleFilterLeads
        try {
          await handleFilterLeads(term, controller);
          // Search is complete, loading state should be handled by handleFilterLeads
        } catch (error) {
          if (error.name !== "AbortError") {
            console.error("Search error:", error);
          }
        }
      } else {
        // Cancellation or empty search term: reset list
        if (abortController) abortController.abort();

        const controller = new AbortController();
        setAbortController(controller);

        if (isCancellation || term === "") {
          // Always refetch original data
          await fetchData(controller);
          // Reset external search value state if provided
          setSearchValue("");
        }
      }
    },
    [abortController, handleFilterLeads, fetchData, setSearchValue]
  );

  const handleSourceIconFilterClick = async (source) => {
    setSelectedSource(source);
    // Check if handleSourceFilter exists before calling it
    if (handleSourceFilter) {
      handleSourceFilter(source);
    } else {
      // Fallback to direct API call if handleSourceFilter is not provided
      setLoading(true);
      try {
        filterLeadsBySource({
          status: filterStatus !== "all" ? filterStatus.toString() : null,
          source: source.toString(),
          recordsPerPage,
          page: currentPage,
        });
      } catch (error) {
        console.error("Error fetching leads:", error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleExportLeads = () => {
    handleExportLeadsThunk();
  };

  // Common button render function
  const renderStatusButton = (status, isMobile = false) => {
    const isActive = filterStatus === status.value;
    const button = (
      <Button
        key={status.key}
        variant="link"
        className={`status-button ${
          isActive ? "status-button-active" : "status-button-default"
        }`}
        style={{
          background: isActive ? status.color : "transparent",
          borderColor: status.color,
          borderWidth: "1.5px",
          borderStyle: "solid",
          transition: "all 0.3s ease",
          textDecoration: "none",
        }}
        onMouseEnter={(e) => {
          if (!isActive) {
            e.currentTarget.style.background = status.color;
            e.currentTarget.style.color = "#fff";
          }
        }}
        onMouseLeave={(e) => {
          if (!isActive) {
            e.currentTarget.style.background = "transparent";
            e.currentTarget.style.color = status.color;
          }
        }}
        disabled={loading}
        onClick={() => {
          if (loading) return;
          handleFilterStatus(status.value);
          setFilterStatus(status.value);
          if (isMobile) {
            setSearchValue("");
          }
        }}
      >
        <span
          style={{
            color: isActive ? "#fff" : status.color,
            marginRight: "4px",
            display: "inline-flex",
            alignItems: "center",
          }}
        >
          {status.label}
        </span>
        <span
          style={{
            color: isActive ? "#fff" : status.color,
          }}
        >
          {status.count}
        </span>
      </Button>
    );
    return status.tooltip ? (
      <OverlayTrigger
        key={status.key}
        placement="top"
        overlay={
          <BSTooltip id={`tooltip-${status.key}`}>{status.tooltip}</BSTooltip>
        }
      >
        <span>{button}</span>
      </OverlayTrigger>
    ) : (
      button
    );
  };

  return (
    <>
      <CenteredModal
        size={"lg"}
        show={showImportLeadModal}
        children={
          <ImportLeadsDropZone
            handleClose={() => setShowImportLeadModal(false)}
            handleImportLeads={handleImportLeads}
          />
        }
        onHide={() => setShowImportLeadModal(false)}
      />
      {/* Always show status filter row */}
      <Row
        className={
          "justify-content-center align-items-center justify-content-md-between"
        }
      >
        <Col lg={9}>
          <ButtonGroup className="mb-2 flex-wrap status-btns">
            {statusOptions.map((status) => renderStatusButton(status, false))}
          </ButtonGroup>
        </Col>
        <Col lg={3}>
          <SearchComponent
            onSearch={handleSearch}
            handleFilterLeads={handleFilterLeads}
            placeholder={t("tableControls.placeholders.searchTable")}
            value={searchValue}
            setValue={setSearchValue}
            abortController={abortController}
            setAbortController={setAbortController}
            disabled={loading}
          />
        </Col>
      </Row>
      <Row className={"mt-3 justify-content-between align-items-center"}>
        <Col xl={6} lg={8} className="social-filter-wrapper my-3">
          <div className="social-filter-container">
            <div
              onClick={() => {
                if (loading) return;
                handleClearFilters();
              }}
              className={`reset${
                selectedSource === null ? " reset-selected" : ""
              }${loading ? " disabled" : ""}`}
              style={{
                pointerEvents: loading ? "none" : "auto",
                opacity: loading ? 0.5 : 1,
              }}
            >
              {selectedSource === null ? (
                t("leadsTable.columns.leadsfrom")
              ) : (
                <div
                  className={
                    "d-flex justify-content-between align-items-center"
                  }
                >
                  <div className={"me-2"}>
                    <IoReturnUpBackOutline size={20} />
                  </div>
                  <div>{t("leadsTable.columns.backToAll")}</div>
                </div>
              )}
            </div>
            {orderedSourceKeys.map((source, index) => (
              <div
                key={index}
                className={`social-icon${
                  selectedSource === source ? " selected" : ""
                }${loading ? " disabled" : ""}`}
                onClick={() => {
                  if (loading) return;
                  handleSourceIconFilterClick(source);
                }}
                style={{
                  pointerEvents: loading ? "none" : "auto",
                  opacity: loading ? 0.5 : 1,
                }}
              >
                <ReactSVG src={sourceToIcon[source]} />
              </div>
            ))}
          </div>
        </Col>
        <Col
          className={
            minimal
              ? "d-flex justify-content-lg-end align-items-center"
              : "d-flex justify-content-center justify-content-lg-end align-items-center"
          }
          xl={6}
          lg={4}
        >
          {!minimal && (
            <div
              className={`clear-filter${loading ? " disabled" : ""}`}
              onClick={() => {
                if (loading) return;
                handleClearFilters();
              }}
              style={{
                pointerEvents: loading ? "none" : "auto",
                opacity: loading ? 0.5 : 1,
              }}
            >
              <FaFilterCircleXmark size={25} />
            </div>
          )}
          {!minimal && currentUserPermissions?.includes("lead-create") && (
            <>
              <span
                className={"import-leads"}
                onClick={() => setShowImportLeadModal(true)}
              >
                <LuImport size={25} />
              </span>
              <span className={"export-leads"} onClick={handleExportLeads}>
                <PiExportBold size={25} />
              </span>
              <div className={"add-lead"} onClick={handleShow}>
                <FaUserPlus size={25} />
              </div>
              <Tooltip
                anchorSelect=".clear-filter"
                content={t("tableControls.tooltips.clearFilters")}
              />
              <Tooltip
                anchorSelect=".add-lead"
                content={t("tableControls.tooltips.addRecord")}
              />
              <Tooltip
                anchorSelect=".import-leads"
                content={t("tableControls.tooltips.importData")}
              />
              <Tooltip
                anchorSelect=".export-leads"
                content={t("tableControls.tooltips.exportData")}
              />
              <CreateClientModal show={showCreateModal} onHide={handleClose} />
            </>
          )}
        </Col>
      </Row>
    </>
  );
};

export default TableControllers;
