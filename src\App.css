.mainColor {
  color: #92C020;
}

.mainColorBgLight {
  background-color: #CAD511;
}

.mainColorLight {
  color: #CAD511;
}

.icon-hover-success {
  color: #92C020;
}

.icon-hover-success {
  color: #92C020;
  border-radius: 0 0 17px 0;
  box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.1);
  background: rgb(255, 255, 255);
}

.icon-hover-success:hover {
  color: white;
  background-color: #92C020;
  transition: all 0.3s ease-in-out;
}

.mainBgColor {
  background-color: #92C020 !important;
}

/**
 * Multi-Language Font System Configuration
 *
 * Features:
 * - Supports Latin (Kumbh Sans) and Arabic (Cairo) typefaces
 * - Organized font directory structure
 * - CSS Custom Properties for maintainability
 * - Direction-aware font selection
 */

/* ==========================================================================
   Kumbh Sans (Latin Typeface)
   Path: /fonts/kumbh-sans/
   ========================================================================== */

   @font-face {
    font-family: 'Kumbh Sans';
    src:
      local('Kumbh Sans Light'),
      url('./assets/fonts/kumbh-sans/KumbhSans-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Kumbh Sans';
    src:
      local('Kumbh Sans Regular'),
      url('./assets/fonts/kumbh-sans/KumbhSans-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Kumbh Sans';
    src:
      local('Kumbh Sans Bold'),
      url('./assets/fonts/kumbh-sans/KumbhSans-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  /* ==========================================================================
     Cairo (Arabic Typeface)
     Path: /fonts/cairo/
     ========================================================================== */

  @font-face {
    font-family: 'Cairo';
    src:
      local('Cairo Light'),
      url('./assets/fonts/cairo/Cairo-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Cairo';
    src:
      local('Cairo Regular'),
      url('./assets/fonts/cairo/Cairo-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Cairo';
    src:
      local('Cairo Medium'),
      url('./assets/fonts/cairo/Cairo-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Cairo';
    src:
      local('Cairo SemiBold'),
      url('./assets/fonts/cairo/Cairo-SemiBold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Cairo';
    src:
      local('Cairo Bold'),
      url('./assets/fonts/cairo/Cairo-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  /* ==========================================================================
     Typography System Configuration
     ========================================================================== */

  :root {
    --typeface-base: 'Kumbh Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  [dir="rtl"] {
    --typeface-base: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* ==========================================================================
     Base Typography Styles
     ========================================================================== */

  body {
    font-family: var(--typeface-base);
    font-weight: 400;
    line-height: 1.5;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
  }

  .content-wrapper {
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

body {
  background: #EBEBEB !important;
}

.content-container {
  border-radius: 14px;
  border: 2px solid #FFF;
  background: #FFF;
  box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.singIn-singOut-Container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
}

a {
  text-decoration: none !important;
}

.required:after {
  content:" *";
  color: red;
}

.grey-suit {
  color: #848199;
}

.bg-grey-suit {
  background-color: rgb(229, 229, 229) !important;
}

.checkbox-success input[type="checkbox"]:checked {
    background-color: #92C020;
    border-color: #92C020;
}

.checkbox-success input[type="checkbox"]:focus {
  border-color: #92C020;
  box-shadow: 0 0 0 0.2rem rgba(146, 192, 32, 0.25);
}

input[type="password"] {
  font:small-caption;
  font-size:16px;
}


.mainScroll::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  background-color: #F5F5F5;
  border-radius: 10px;
}

.mainScroll::-webkit-scrollbar
{
  width: 10px;
  background-color: #F5F5F5;
}

.mainScroll::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background-color: #FFF;
  background-image: -webkit-gradient(linear,
  40% 0%,
  75% 84%,
  from(#4D9C41),
  to(#19911D),
  color-stop(.6,#54DE5D))
}

.flex-centered {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-tabs.nav-underline .nav-link.active, .admin-tabs.nav-underline .show>.nav-link {
  color: #FFF;
  border-bottom: 2px solid #92C020;
}

.admin-tabs.nav-underline .nav-link, .admin-tabs.nav-underline .show>.nav-link {
  color: #888;
  border-bottom: unset;
}

.gradient-border {
  --border-width: 3px;

  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  background: #222;
  border-radius: var(--border-width);

  &::after {
    position: absolute;
    content: "";
    top: calc(-1 * var(--border-width));
    left: calc(-1 * var(--border-width));
    z-index: -1;
    width: calc(100% + var(--border-width) * 2);
    height: calc(100% + var(--border-width) * 2);
    background: linear-gradient(
            60deg,
            hsl(224, 85%, 66%),
            hsl(269, 85%, 66%),
            hsl(314, 85%, 66%),
            hsl(359, 85%, 66%),
            hsl(44, 85%, 66%),
            hsl(89, 85%, 66%),
            hsl(134, 85%, 66%),
            hsl(179, 85%, 66%)
    );
    background-size: 300% 300%;
    background-position: 0 50%;
    border-radius: calc(2 * var(--border-width));
    animation: moveGradient 4s alternate infinite;
  }
}

@keyframes moveGradient {
  50% {
    background-position: 100% 50%;
  }
}

@keyframes spin {
  to {
    --angle: 360deg;
  }
}

.badge-warning {
  color: #212529;
  background-color: #ffc107;
}

.badge-danger {
  color: #fff;
  background-color: #dc3545;
}

.badge-primary {
  color: #fff;
  background-color: #007bff;
}

.badge-success {
  color: #fff;
  background-color: #28a745;
}

button.apply-btn {
  border-radius: 39px;
  border: 1px solid #DFDFDF;
  background: #92C020;
  color: #FFF;
  font-size: 1rem;
  font-weight: 500;
}

button.clear-btn {
  border-radius: 39px;
  background: rgba(146, 192, 32, 0.20);
  color: #92C020;
  font-size: 01rem;
  font-weight: 500;
  border: 1px solid #DFDFDF;
}

.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.submit-btn {
  border: 1px solid #92C020;
  color: #ffffff;
  font-size: 0.8rem;
  font-weight: bold;
  padding: 12px 25px;
  letter-spacing: 1px;
  border-radius: 10px;
  background: linear-gradient(263deg, #92C020 -9.91%, #CAD511 128.31%);
  transition: transform 80ms ease-in;
  cursor: pointer;
  width: fit-content;
  border: unset;

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: none;
  }

}

.btn.btn-primary:active, .btn.btn-primary:focus, .btn.btn-primary.show, .btn.btn-primary:hover {
  background-color: #92C020;
  border-color: #4D9C41;
  color: #fff;
}

.border-right-dark {
  border-right: 1px solid black;
}

.border-left-dark {
  border-left: 1px solid black;
}

.doughnut-team-chart .chartjs-tooltip , .bar-team-chart .chartjs-tooltip, .line-team-chart .chartjs-tooltip, .doughnut-lead-chart .chartjs-tooltip, .line-sales-chart .chartjs-tooltip, .line-opportunities-chart .chartjs-tooltip, .doughnut-opportunities-chart .chartjs-tooltip, .sales-bar-chart .chartjs-tooltip {
  position: absolute;
  background-color: white;
  color: black;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid white;
  pointer-events: none;
  font-size: 1rem;
  font-weight: 500;
  white-space: nowrap;
  z-index: 999;
  transition: ease-in-out all 0.25s;
}

.admin-theme .line-opportunities-chart .chartjs-tooltip {
  background-color: rgba(192,192,192,0.2);
  border: unset;
  padding: unset;
}

.admin-theme .line-opportunities-chart .chartjs-tooltip table {
  display: flex;
  flex-direction: column;
  justify-content: start;
  padding: 0.5rem;
}

.admin-theme .line-opportunities-chart .chartjs-tooltip table .chartjs-tooltip-body{
  padding: 0.5rem;
}


.chartjs-tooltip table {
  padding: 1rem;
}

.lead-chart-container {
  max-width: 400px;
}

.team-chart-container {
  max-width: 400px;
}

.admin-theme.content-container {
  border: 1px solid #444;
  background: #242424;
}

.labels-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  /*margin-top: 20px;*/
}

.label-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.sticky-column {
  position: sticky;
  left: 0;
  background-color: white;
  z-index: 1;
  border-right: 1px solid #ddd;
}

.social-icon-container svg {
  max-width: 21px;
  max-height: 21px;
}

.dark-mode .pagination-title {
  color: white;
}

[dir="rtl"] .page-title {
  text-align: right;
}

.page-title {
  margin: 2rem 0;
  text-align: left;
  @media only screen and (max-width: 768px) {
    text-align: center;
  }
}

.text-container-truncated {
  display: -webkit-box;
  -webkit-line-clamp: 4; /* Number of lines to show */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5; /* Adjust the line height based on your design */
  max-height: calc(4 * 1.5em); /* 4 lines with the same line height */
}

.pointer {
  cursor: pointer;
}

.searchBadgeRight {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 40px;
  border-radius: 0 50px 50px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

[dir="rtl"] .searchBadgeRight {
  right: auto;
  left: 0;
  border-radius: 50px 0 0 50px;
}

.searchBadgeRight.searching {
  background-color: #dc3545 !important; /* Bootstrap danger color */
}

.searchBadgeRight.searching:hover {
  background-color: #bb2d3b !important; /* Darker shade for hover */
}

/* Add padding to input to prevent text from going under the button */
.search-input-rtl {
  padding-right: 45px !important;
}

[dir="rtl"] .search-input-rtl {
  padding-right: 1rem !important;
  padding-left: 45px !important;
}

/* Base status badge styles */
.status-badge {
  display: inline-block;
  border-radius: 25px;
  font-size: 0.5rem;
  text-transform: capitalize;
  white-space: nowrap;
}

/* Status specific styles */
.status-badge--pending {
  background-color: #fff59d;
  color: #d3851d;
}

.status-badge--in-progress {
  background-color: #e6f4ff;
  color: #007bff;
}

.status-badge--completed {
  background-color: #d1e7dd;
  color: #155724;
}

.status-badge--rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge--wrong-lead {
  background-color: #fff0f0;
  color: #a52a2a;
}

.status-badge--not-qualified {
  background-color: #f5f5f5;
  color: #6c757d;
}

.status-badge--no-communication {
  background-color: #ffeeba;
  color: #8a6d3b;
}

.status-badge--booked {
  background-color: #d1ecf1;
  color: #004085;
}

.status-badge--booked-reserved {
  background-color: #cfe2f3;
  color: #002d62;
}

.status-badge--canceled {
  background-color: #e2e3e5;
  color: #343a40;
}

.status-badge--quotation-sent {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge--unknown {
  background-color: #f5f5f5;
  color: #6c757d;
  opacity: 0.7;
}

[dir="rtl"] .date-picker-container input {
  text-align: right;
  padding-right: 30px;
}

[dir="ltr"] .date-picker-container input {
  text-align: left;
  padding-left: 30px;
}

[dir="rtl"] .search-input-rtl {
  padding-right: 2.5rem;
}

.gradient-border {
  text-align: center;
  position: relative;
}

.gradient-border::after, .gradient-border::before{
  content: '';
  position: absolute;
  height: 100%;
  width: 100%;
  background-image: conic-gradient(from var(--angle), #ff4545, #00ff99, #006aff, #ff0095, #ff4545);
  top: 50%;
  left: 50%;
  translate: -50% -50%;
  z-index: -1;
  padding: 3px;
  border-radius: 1.5rem;
  animation: 3s spin linear infinite;
}
.gradient-border::before{
  filter: blur(1.5rem);
  opacity: 0.5;
}
@property --angle {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}

@keyframes spin{
  from{
    --angle: 0deg;
  }
  to{
    --angle: 360deg;
  }
}

.radial-gradient-border {
  position: relative;
  padding: 20px;
  border-radius: 10px;
  /* margin-bottom: 20px; */
  box-sizing: border-box;
}

div.radial-gradient-border p {
  font-weight: 700;
  position: relative;
  z-index: 2;
}

@property --angle {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}

@keyframes spin {
  0% {
    --angle: 0deg;
  }
  100% {
    --angle: 360deg;
  }
}

.radial-gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 10px;
  background: conic-gradient(from var(--angle), #92C020, #CAD511, #243334, #5F7475, #92C020);
  animation: spin 3s linear infinite;
  z-index: 0;
  mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask: linear-gradient(#fff 0 0) content-box,
               linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  padding: 3px;
}

/* Make sure content is above the border */
.radial-gradient-border > * {
  position: relative;
  z-index: 1;
}

.cancel-btn {
  outline: 1px solid #dc3545;
  color: #dc3545;
  font-size: 12px;
  font-weight: bold;
  padding: 12px 45px;
  letter-spacing: 1px;
  border-radius: 30px;
  background-color: #ffffff;
  transition: transform 80ms ease-in;
  cursor: pointer;
  width: fit-content;
  border: unset;

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: none;
    background-color: #dc3545;
    color: #ffffff;
  }

  &:hover {
    background-color: #dc3545;
    color: #ffffff;
    outline: none;
  }
}

/* ==========================================================================
   Form Switch Styles - Generalized for all switches
   ========================================================================== */

/* Base switch styles */
.form-switch {
    padding-left: 2.5em;
}

.form-switch .form-check-input {
    height: 1.5em;
    width: 2.75em;
    cursor: pointer;
    margin-left: -2.5em;
    background-position: left center;
    transition: background-position .15s ease-in-out;
}

.form-switch .form-check-input:checked {
    background-position: right center;
    background-color: #92C020;  /* Using your main brand color */
    border-color: #92C020;
}

/* RTL Support */
[dir="rtl"] .form-switch {
    padding-right: 2.5em;
    padding-left: 0;
}

[dir="rtl"] .form-switch .form-check-input {
    float: right;
    margin-right: -2.5em;
    margin-left: 0;
}

[dir="rtl"] .form-switch .form-check-input:checked {
    background-position: left center;
}

/* Switch sizes */
.form-switch.form-switch-lg .form-check-input {
    height: 2em;
    width: 3.75em;
}

.form-switch.form-switch-sm .form-check-input {
    height: 1em;
    width: 1.75em;
}

/* Disabled state */
.form-switch .form-check-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Focus state */
.form-switch .form-check-input:focus {
    border-color: #92C020;
    box-shadow: 0 0 0 0.25rem rgba(146, 192, 32, 0.25);
}

/* ==========================================================================
   End Form Switch Styles
   ========================================================================== */

/* ==========================================================================
   Modal Styles - Generalized for all modals
   ========================================================================== */

/* Modal Header Close Button Positioning */
.modal-header {
    position: relative;
}

.modal-header .btn-close {
    position: absolute;
    right: 1rem;
    margin: 0;
    padding: 1rem;
}

/* RTL Support for Modal Close Button */
[dir="rtl"] .modal-header .btn-close {
    right: auto;
    left: 1rem;
}

/* Ensure modal title has proper spacing */
.modal-title {
    width: 100%;
    padding-right: 2rem;  /* Space for close button */
}

[dir="rtl"] .modal-title {
    padding-right: 0;
    padding-left: 2rem;
}

/* Optional: Custom close button styling */
.modal-header .btn-close:focus {
    box-shadow: 0 0 0 0.25rem rgba(146, 192, 32, 0.25); /* Using your brand color */
}

/* ==========================================================================
   End Modal Styles
   ========================================================================== */


.quota-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;  /* equivalent to gap-2 in Bootstrap */
    flex-direction: row;
    margin-bottom: 1.5rem;  /* equivalent to mb-4 in Bootstrap */
    @media (max-width: 768px) {
        flex-direction: column;
    }
}

/* LTR validation styles */
[dir="ltr"] .form-control.is-valid {
  border-color: #80AA17;
  padding-right: 10px;
  padding-left: 10px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2380AA17' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(.375em + .1875rem) center;
  background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

[dir="ltr"] .form-control.is-invalid {
  border-color: #dc3545;
  padding-right: 10px;
  padding-left: 10px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(.375em + .1875rem) center;
  background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

/* RTL validation styles */
[dir="rtl"] .form-control.is-valid {
  border-color: #80AA17;
  padding-left: calc(1.5em + .75rem);
  padding-right: 0.75rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2380AA17' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: left calc(.375em + .1875rem) center;
  background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

[dir="rtl"] .form-control.is-invalid {
  border-color: #dc3545;
  padding-left: calc(1.5em + .75rem);
  padding-right: 0.75rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: left calc(.375em + .1875rem) center;
  background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

/* Password input specific styles */
.password-input-container .form-control.is-valid,
.password-input-container .form-control.is-invalid {
  background-position: right calc(.375em + .1875rem + 35px) center;
}

[dir="rtl"] .password-input-container .form-control.is-valid,
[dir="rtl"] .password-input-container .form-control.is-invalid {
  background-position: left calc(.375em + .1875rem + 35px) center;
}

/* Phone input specific styles */
.validated-phone-input-container.rtl .PhoneInput.is-valid .PhoneInputInput,
.validated-phone-input-container.rtl .PhoneInput.is-invalid .PhoneInputInput {
  padding-right: 0;
  padding-left: calc(1.5em + .75rem);
  background-position: left calc(.375em + .1875rem) center;
}

/* Update eye icon positioning */
.password-input-icon {
  position: absolute;
  right: 10px;
  /*top: 50%;*/
  transform: translateY(-50%);
  cursor: pointer;
  color: #00000033;
  /* Add z-index to ensure it's above the input but below validation icon */
  z-index: 2;
}

.password-input-icon-TM {
  position: absolute;
  right: 0;
  top: 75%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  color: #00000083;
}

.password-input-container:has(input.is-valid) .password-input-icon-TM {
  top: 75%;
}

.password-input-container:has(input.is-invalid) .password-input-icon-TM {
  top: 53%;
}


/* Position the validation icon */
.password-input-container .form-control.is-invalid {
  background-position: right calc(.375em + .1875rem + 35px) center !important; /* Move validation icon further right */
}

/* RTL support */
[dir="rtl"] .password-input-icon {
  right: auto;
  left: 10px;
}

/* RTL support */
[dir="rtl"] .password-input-icon-TM {
  right: auto;
  left: 1.2rem;
}

[dir="rtl"] .password-input-container .form-control.is-invalid {
  background-position: left calc(.375em + .1875rem + 35px) center !important;
}

/* Ensure validation message doesn't overlap */
.password-input-container .invalid-feedback {
  margin-top: 0.25rem;
  display: block;
}

/* Update icon states for validation */
.password-input-container:has(input.is-invalid) .password-input-icon {
  /*top: 50%;*/
  transform: translateY(-50%);
}

.password-input-container:has(input.is-invalid) .password-input-icon-TM {
  /*top: 50%;*/
  transform: translate(-50%, -50%);
}

[dir="rtl"] .password-input-container:has(input.is-invalid) .password-input-icon-TM {
  /*top: 50%;*/
  transform: translate(-35%, -50%);
}

/* Optional: Style adjustments for the eye icon */
.password-toggle-icon {
  opacity: 0.6;
  transition: opacity 0.2s;
}

.password-toggle-icon:hover {
  opacity: 1;
}

[dir="ltr"] .new-member-field .form-control.is-invalid, .was-validated .form-control:invalid {
  padding-left: calc(0.5em + .1rem);
}


[dir="rtl"] .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"], .was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"] {
  padding-left: calc(0.5em + .1rem);
}

.member-modal-close-icon {
  position: absolute;
  top: 27%;
  right: 0;
  transform: translate(-50%, -50%);
  cursor: pointer;
}

.members-status-switch .form-check-input {
  min-height: 25px;
  min-width: 50px;
  background-color: #E35757;
  color: white;
}

.members-status-switch.form-switch .form-check-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%27-4 -4 8 8%27%3e%3ccircle r=%273%27 fill=%27%23fff%27/%3e%3c/svg%3e");
}

.members-status-switch .form-check-input:checked {
  background-color: #92C020;
  border-color: #92C020;
}

.members-status-switch .form-switch .form-check-input {
  margin-top: 0;
}

.admin-theme .pagination_title {
  color: #ffff;
}
