import adidasPage from "../../assets/media/Icons/adidas_page.svg";
import { FaFlag } from "react-icons/fa";
import "./IntegratedPlatformPages.css";
import { Col, Row } from "react-bootstrap";
import { toast } from "react-toastify";
import { FaEye } from "react-icons/fa6";
import { useState } from "react";
import { HiOutlineCloudArrowDown } from "react-icons/hi2";
import metaService from "../../services/integrations/meta";
import { useTranslation } from "react-i18next";

const IntegratedPlatformPages = ({
  accountsPages,
  setForms,
  setLoading,
  setPageAccessTokens,
}) => {
  const { t } = useTranslation();
  const [activePage, setActivePage] = useState(null);

  const getForms = async (data) => {
    setLoading(true);
    try {
      const response = await metaService.getFormsApi(data);
      setForms(response?.data);
      setLoading(false);
    } catch (e) {
      toast.error(e?.response?.data?.message, {
        position: "bottom-right",
        theme: "dark",
      });
      setLoading(false);
    }
  };

  const getLeadsFromPage = async (data) => {
    try {
      await metaService.getLeadsByPageApi(data);
      toast.success(t('integrations.facebook.pages.leadsImported'), {
        position: "bottom-right",
        theme: "dark",
      });
    } catch (e) {
      toast.error(e?.response?.data?.message, {
        position: "bottom-right",
        theme: "dark",
      });
    }
  };

  return (
    <>
      <Row className={"justify-content-start mb-3 gap-4"}>
        {accountsPages?.map((accountPage) => (
          <Col
            lg={3}
            md={4}
            sm={6}
            xs={12}
            className={"connected-page-content my-2 pt-3 px-0 shadow-lg"}
            key={accountPage?.id}
          >
            <div className={"position-relative"}>
              <img
                width={50}
                height={50}
                src={
                  accountPage?.picture?.data?.url
                    ? accountPage?.picture?.data?.url
                    : accountPage?.picture || adidasPage
                }
                className={"integration-account rounded-circle"}
              />
              <div className={"user-integration-page px-2 rounded-circle"}>
                <FaFlag size={10} color="white" />
              </div>
            </div>
            <p className={"fw-bold fs-5 mt-1"}>{accountPage?.name}</p>
            <div className={"d-flex justify-content-between facebook-page"}>
              <div className={"form-icon-integration-l py-2"}>
                <HiOutlineCloudArrowDown
                  title={t('integrations.facebook.pages.getLeads')}
                  role={"button"}
                  size={25}
                  onClick={() => {
                    getLeadsFromPage({
                      page_id: accountPage?.id,
                      page_access_token: accountPage?.access_token,
                    });
                    setPageAccessTokens(accountPage?.access_token);
                  }}
                />
              </div>
              <div
                className={`form-icon-integration-r ${activePage === accountPage?.id ? "active-page" : ""} py-2`}
              >
                <FaEye
                  title={t('integrations.facebook.pages.showForms')}
                  role={"button"}
                  size={25}
                  onClick={() => {
                    getForms({
                      page_id: accountPage?.id,
                      page_access_token: accountPage?.access_token,
                    });
                    setPageAccessTokens(accountPage?.access_token);
                    setActivePage(accountPage?.id);
                  }}
                />
              </div>
            </div>
          </Col>
        ))}
        {/*<div className={'green-label connected-count'}>(7) lead forms connected</div>*/}
      </Row>
    </>
  );
};

export default IntegratedPlatformPages;
