import { useCallback, useState, useEffect } from "react";
import { Container } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import leadService from "../../../services/leads";
import TableControllers from "../../CustomDataTable/TableControllers";
import PaginationComponent from "../../CustomDataTable/PaginationComponent";
import FilterTableNoCommLeadsComponent from "./FilterTableNoCommLeadsComponent";
import {
  setTotalPages,
  setCurrentPage,
  setRecordsPerPage,
  setTotal,
  setPaginationLinks,
} from "../../../redux/features/leadsPaginationSlice";
import { showErrorToast } from "../../../utils/toast-success-error";
import "../AllClients/Clients.css";
import "../../CustomDataTable/datatable.css";
import useClient from "../../../redux/hooks/useClient";
import FetchingDataLoading from "../../LoadingAnimation/FetchingDataLoading";

// Component to list leads that have no communication
const NoCommunicationLeadsComponent = () => {
  const dispatch = useDispatch();

  // Redux selectors
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );

  const { noCommunicationLeads, setNoCommunicationLeads, setLeadStatusCounts } =
    useClient();

  const [selectedSourceLocal, setSelectedSourceLocal] = useState(null);
  const [filterStatusLocal, setFilterStatusLocal] = useState("all");

  const [loading, setLoading] = useState(true);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [dataFetched, setDataFetched] = useState(false);

  // Add a new state for search term
  const [searchTerm, setSearchTerm] = useState("");
  // Initial fetch and on pagination/filter change
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        const leadsData = await leadService.getNoCommunicationLeadsApi(
          undefined,
          recordsPerPage,
          currentPage,
          selectedSourceLocal,
          filterStatusLocal !== "all" ? filterStatusLocal : null,
          searchTerm
        );

        if (leadsData && leadsData.data) {
          const leadsArray = leadsData.data["All Leads"] || [];
          const pageNumber = leadsData.data["Page Number"] || 1;
          const numberOfPages = leadsData.data["Number Of Pages"] || 1;

          const {
            completed = 0,
            inprogress = 0,
            pendding = 0,
            rejected = 0,
            assigned = 0,
          } = leadsData.data;

          setLeadStatusCounts({
            completed,
            assigned,
            inprogress,
            pendding,
            rejected,
          });
          setNoCommunicationLeads(leadsArray);
          dispatch(setTotalPages(numberOfPages));
          dispatch(setCurrentPage(pageNumber));
          dispatch(setRecordsPerPage(recordsPerPage));
          dispatch(setTotal(leadsArray.length * numberOfPages));
          dispatch(setPaginationLinks([]));
          setDataFetched(true);
        }
      } catch (error) {
        showErrorToast(error.response?.data?.message || "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [
    currentPage,
    recordsPerPage,
    selectedSourceLocal,
    filterStatusLocal,
    searchTerm,
  ]);

  // Create a simple fetchData function that triggers a re-fetch
  const fetchData = useCallback(() => {
    // This function doesn't need to do anything - the useEffect above
    // will automatically re-run when dependencies change
    // We just need this function to exist for compatibility with TableControllers
  }, []);

  // handle filter leads (search term). We'll reuse AllLeads logic simplified
  const handleFilterLeads = async (term) => {
    // Prevent multiple simultaneous requests
    if (loading) return;

    try {
      setLoading(true);
      setSearchTerm(term); // update search term state

      if (!term || term.length === 0) {
        setIsSearchActive(false);
        setSearchTerm(""); // This will trigger the useEffect to re-fetch
        return;
      }

      setIsSearchActive(true);
      // Don't reset other filters, just update searchTerm
      const leadsData = await leadService.getNoCommunicationLeadsApi(
        undefined,
        recordsPerPage,
        currentPage,
        selectedSourceLocal,
        filterStatusLocal !== "all" ? filterStatusLocal : null,
        term // search
      );
      if (leadsData && leadsData.data) {
        const leadsArray = leadsData.data["All Leads"] || [];
        const pageNumber = leadsData.data["Page Number"] || 1;
        const numberOfPages = leadsData.data["Number Of Pages"] || 1;
        // Extract status counts from response (if present)
        const {
          completed = 0,
          inprogress = 0,
          pendding = 0,
          rejected = 0,
          assigned = 0,
        } = leadsData.data;
        setLeadStatusCounts({
          completed,
          assigned,
          inprogress,
          pendding,
          rejected,
        });
        setNoCommunicationLeads(leadsArray);
        dispatch(setTotalPages(numberOfPages));
        dispatch(setCurrentPage(pageNumber));
        dispatch(setRecordsPerPage(recordsPerPage));
        dispatch(setTotal(leadsArray.length * numberOfPages));
        dispatch(setPaginationLinks([]));
        setDataFetched(true);
      }
    } catch (error) {
      console.error("Error fetching leads:", error);
      showErrorToast(error.response?.data?.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  // Add status filter handler
  const handleFilterStatus = useCallback(
    async (status) => {
      // Prevent multiple simultaneous requests
      if (loading) return;

      try {
        setLoading(true);
        setFilterStatusLocal(status);
        setIsSearchActive(false);

        const response = await leadService.getNoCommunicationLeadsApi(
          undefined,
          recordsPerPage,
          currentPage,
          selectedSourceLocal,
          status !== "all" ? status : null,
          searchTerm // keep current search term
        );
        if (response && response.data) {
          const leadsArray = response.data["All Leads"] || [];
          const pageNumber = response.data["Page Number"] || 1;
          const numberOfPages = response.data["Number Of Pages"] || 1;
          // Extract status counts from response (if present)
          const {
            completed = 0,
            inprogress = 0,
            pendding = 0,
            rejected = 0,
            assigned = 0,
          } = response.data;
          setLeadStatusCounts({
            completed,
            assigned,
            inprogress,
            pendding,
            rejected,
          });
          setNoCommunicationLeads(leadsArray);
          dispatch(setTotalPages(numberOfPages));
          dispatch(setCurrentPage(pageNumber));
          dispatch(setRecordsPerPage(recordsPerPage));
          dispatch(setTotal(leadsArray.length * numberOfPages));
          dispatch(setPaginationLinks([]));
          setDataFetched(true);
        }
      } catch (error) {
        showErrorToast(error.response?.data?.message || "An error occurred");
      } finally {
        setLoading(false);
      }
    },
    [recordsPerPage, currentPage, selectedSourceLocal, searchTerm]
  );

  // Add source filter handler
  const handleSourceFilter = useCallback(
    async (source) => {
      // Prevent multiple simultaneous requests
      if (loading) return;

      try {
        setLoading(true);
        setSelectedSourceLocal(source);
        setIsSearchActive(false);

        const response = await leadService.getNoCommunicationLeadsApi(
          undefined,
          recordsPerPage,
          currentPage,
          source,
          filterStatusLocal !== "all" ? filterStatusLocal : null,
          searchTerm // keep current search term
        );
        if (response && response.data) {
          const leadsArray = response.data["All Leads"] || [];
          const pageNumber = response.data["Page Number"] || 1;
          const numberOfPages = response.data["Number Of Pages"] || 1;
          // Extract status counts from response (if present)
          const {
            completed = 0,
            inprogress = 0,
            pendding = 0,
            rejected = 0,
            assigned = 0,
          } = response.data;
          setLeadStatusCounts({
            completed,
            assigned,
            inprogress,
            pendding,
            rejected,
          });
          setNoCommunicationLeads(leadsArray);
          dispatch(setTotalPages(numberOfPages));
          dispatch(setCurrentPage(pageNumber));
          dispatch(setRecordsPerPage(recordsPerPage));
          dispatch(setTotal(leadsArray.length * numberOfPages));
          dispatch(setPaginationLinks([]));
          setDataFetched(true);
        }
      } catch (error) {
        showErrorToast(error.response?.data?.message || "An error occurred");
      } finally {
        setLoading(false);
      }
    },
    [recordsPerPage, currentPage, filterStatusLocal, searchTerm]
  );

  const handleResetAllFilters = useCallback(() => {
    setSelectedSourceLocal(null);
    setFilterStatusLocal("all");
    setSearchTerm("");
    setIsSearchActive(false);
  }, []);

  return (
    <Container className="all-leads-table p-4">
      {/* Show loading animation only for initial fetch */}
      {loading && !dataFetched ? (
        <FetchingDataLoading />
      ) : (
        <>
          <TableControllers
            minimal
            setLoading={setLoading}
            loading={loading}
            abortController={undefined}
            fetchData={fetchData}
            handleFilterLeads={handleFilterLeads}
            handleSourceFilter={handleSourceFilter}
            handleFilterStatus={handleFilterStatus}
            setAbortController={() => {}}
            isSearchActive={isSearchActive}
            nocommunication={true}
            onClearAllFilters={handleResetAllFilters}
          />
          <FilterTableNoCommLeadsComponent
            loading={loading}
            data={noCommunicationLeads}
            hideNoData={!dataFetched}
          />
          {/* Pagination visible always */}
          <PaginationComponent />
        </>
      )}
    </Container>
  );
};

export default NoCommunicationLeadsComponent;
