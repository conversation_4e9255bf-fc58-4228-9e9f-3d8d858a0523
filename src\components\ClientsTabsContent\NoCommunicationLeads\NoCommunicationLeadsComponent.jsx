import { useCallback, useState, useEffect } from "react";
import { Container } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import leadService from "../../../services/leads";
import TableControllers from "../../CustomDataTable/TableControllers";
import PaginationComponent from "../../CustomDataTable/PaginationComponent";
import FilterTableNoCommLeadsComponent from "./FilterTableNoCommLeadsComponent";
import {
  setTotalPages,
  setCurrentPage,
  setRecordsPerPage,
  setTotal,
  setPaginationLinks,
} from "../../../redux/features/leadsPaginationSlice";
import { showErrorToast } from "../../../utils/toast-success-error";
import "../AllClients/Clients.css";
import "../../CustomDataTable/datatable.css";
import useClient from "../../../redux/hooks/useClient";
import FetchingDataLoading from "../../LoadingAnimation/FetchingDataLoading";

// Component to list leads that have no communication
const NoCommunicationLeadsComponent = () => {
  const dispatch = useDispatch();

  // Redux selectors
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );

  const { noCommunicationLeads, setNoCommunicationLeads, setLeadStatusCounts } =
    useClient();

  const [selectedSourceLocal, setSelectedSourceLocal] = useState(null);
  const [filterStatusLocal, setFilterStatusLocal] = useState("all");

  const [loading, setLoading] = useState(true);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [dataFetched, setDataFetched] = useState(false);
  const [abortController, setAbortController] = useState(null);

  // Add a new state for search term
  const [searchTerm, setSearchTerm] = useState("");
  // Initial fetch and on pagination/filter change
  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentPage,
    recordsPerPage,
    selectedSourceLocal,
    filterStatusLocal,
    searchTerm,
  ]);

  // Fetch leads that have NO communication
  const fetchData = useCallback(
    async (controller = null) => {
      try {
        setLoading(true);

        // Cancel any ongoing request before starting a new one
        if (abortController && !controller) {
          abortController.abort();
        }

        // Create new controller if not provided
        const requestController = controller || new AbortController();
        if (!controller) {
          setAbortController(requestController);
        }

        const leadsData = await leadService.getNoCommunicationLeadsApi(
          requestController,
          recordsPerPage,
          currentPage,
          selectedSourceLocal,
          filterStatusLocal !== "all" ? filterStatusLocal : null,
          searchTerm // always include current search term
        );
        if (leadsData && leadsData.data) {
          const leadsArray = leadsData.data["All Leads"] || [];
          const pageNumber = leadsData.data["Page Number"] || 1;
          const numberOfPages = leadsData.data["Number Of Pages"] || 1;
          // Extract status counts from response (if present)
          const {
            completed = 0,
            inprogress = 0,
            pendding = 0,
            rejected = 0,
            assigned = 0,
          } = leadsData.data;
          setLeadStatusCounts({
            completed,
            assigned,
            inprogress,
            pendding,
            rejected,
          });
          setNoCommunicationLeads(leadsArray);
          dispatch(setTotalPages(numberOfPages));
          dispatch(setCurrentPage(pageNumber));
          dispatch(setRecordsPerPage(recordsPerPage));
          dispatch(setTotal(leadsArray.length * numberOfPages));
          dispatch(setPaginationLinks([]));
          setDataFetched(true);
        }
      } catch (error) {
        if (error.name !== "AbortError") {
          showErrorToast(error.response?.data?.message || "An error occurred");
        }
      } finally {
        setLoading(false);
      }
    },
    [
      abortController,
      currentPage,
      recordsPerPage,
      selectedSourceLocal,
      filterStatusLocal,
      setNoCommunicationLeads,
      setLeadStatusCounts,
      dispatch,
      searchTerm,
    ]
  );

  // handle filter leads (search term). We'll reuse AllLeads logic simplified
  const handleFilterLeads = async (term, controller = null) => {
    try {
      setLoading(true);

      // Cancel any ongoing request before starting a new one
      if (abortController && !controller) {
        abortController.abort();
      }

      // Create new controller if not provided
      const requestController = controller || new AbortController();
      if (!controller) {
        setAbortController(requestController);
      }

      setSearchTerm(term); // update search term state
      if (!term || term.length === 0) {
        setIsSearchActive(false);
        await fetchData(requestController);
        return;
      }
      setIsSearchActive(true);
      // Don't reset other filters, just update searchTerm
      const leadsData = await leadService.getNoCommunicationLeadsApi(
        requestController,
        recordsPerPage,
        currentPage,
        selectedSourceLocal,
        filterStatusLocal !== "all" ? filterStatusLocal : null,
        term // search
      );
      if (leadsData && leadsData.data) {
        const leadsArray = leadsData.data["All Leads"] || [];
        const pageNumber = leadsData.data["Page Number"] || 1;
        const numberOfPages = leadsData.data["Number Of Pages"] || 1;
        // Extract status counts from response (if present)
        const {
          completed = 0,
          inprogress = 0,
          pendding = 0,
          rejected = 0,
          assigned = 0,
        } = leadsData.data;
        setLeadStatusCounts({
          completed,
          assigned,
          inprogress,
          pendding,
          rejected,
        });
        setNoCommunicationLeads(leadsArray);
        dispatch(setTotalPages(numberOfPages));
        dispatch(setCurrentPage(pageNumber));
        dispatch(setRecordsPerPage(recordsPerPage));
        dispatch(setTotal(leadsArray.length * numberOfPages));
        dispatch(setPaginationLinks([]));
        setDataFetched(true);
      }
    } catch (error) {
      if (error.name !== "AbortError") {
        console.error("Error fetching leads:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  // Add status filter handler
  const handleFilterStatus = useCallback(
    async (status) => {
      try {
        setLoading(true);

        // Cancel any ongoing request before starting a new one
        if (abortController) {
          abortController.abort();
        }

        const requestController = new AbortController();
        setAbortController(requestController);

        setFilterStatusLocal(status);
        setIsSearchActive(false);

        const response = await leadService.getNoCommunicationLeadsApi(
          requestController,
          recordsPerPage,
          currentPage,
          selectedSourceLocal,
          status !== "all" ? status : null,
          searchTerm // keep current search term
        );
        if (response && response.data) {
          const leadsArray = response.data["All Leads"] || [];
          const pageNumber = response.data["Page Number"] || 1;
          const numberOfPages = response.data["Number Of Pages"] || 1;
          // Extract status counts from response (if present)
          const {
            completed = 0,
            inprogress = 0,
            pendding = 0,
            rejected = 0,
            assigned = 0,
          } = response.data;
          setLeadStatusCounts({
            completed,
            assigned,
            inprogress,
            pendding,
            rejected,
          });
          setNoCommunicationLeads(leadsArray);
          dispatch(setTotalPages(numberOfPages));
          dispatch(setCurrentPage(pageNumber));
          dispatch(setRecordsPerPage(recordsPerPage));
          dispatch(setTotal(leadsArray.length * numberOfPages));
          dispatch(setPaginationLinks([]));
          setDataFetched(true);
        }
      } catch (error) {
        if (error.name !== "AbortError") {
          showErrorToast(error.response?.data?.message || "An error occurred");
        }
      } finally {
        setLoading(false);
      }
    },
    [
      abortController,
      recordsPerPage,
      currentPage,
      selectedSourceLocal,
      setNoCommunicationLeads,
      setLeadStatusCounts,
      dispatch,
      searchTerm,
    ]
  );

  // Add source filter handler
  const handleSourceFilter = useCallback(
    async (source) => {
      try {
        setLoading(true);

        // Cancel any ongoing request before starting a new one
        if (abortController) {
          abortController.abort();
        }

        const requestController = new AbortController();
        setAbortController(requestController);

        setSelectedSourceLocal(source);
        setIsSearchActive(false);

        const response = await leadService.getNoCommunicationLeadsApi(
          requestController,
          recordsPerPage,
          currentPage,
          source,
          filterStatusLocal !== "all" ? filterStatusLocal : null,
          searchTerm // keep current search term
        );
        if (response && response.data) {
          const leadsArray = response.data["All Leads"] || [];
          const pageNumber = response.data["Page Number"] || 1;
          const numberOfPages = response.data["Number Of Pages"] || 1;
          // Extract status counts from response (if present)
          const {
            completed = 0,
            inprogress = 0,
            pendding = 0,
            rejected = 0,
            assigned = 0,
          } = response.data;
          setLeadStatusCounts({
            completed,
            assigned,
            inprogress,
            pendding,
            rejected,
          });
          setNoCommunicationLeads(leadsArray);
          dispatch(setTotalPages(numberOfPages));
          dispatch(setCurrentPage(pageNumber));
          dispatch(setRecordsPerPage(recordsPerPage));
          dispatch(setTotal(leadsArray.length * numberOfPages));
          dispatch(setPaginationLinks([]));
          setDataFetched(true);
        }
      } catch (error) {
        if (error.name !== "AbortError") {
          showErrorToast(error.response?.data?.message || "An error occurred");
        }
      } finally {
        setLoading(false);
      }
    },
    [
      abortController,
      recordsPerPage,
      currentPage,
      filterStatusLocal,
      setNoCommunicationLeads,
      setLeadStatusCounts,
      dispatch,
      searchTerm,
    ]
  );

  // Add a function to reset all filters
  const handleResetAllFilters = useCallback(() => {
    // Cancel any ongoing request
    if (abortController) {
      abortController.abort();
    }

    setSelectedSourceLocal(null);
    setFilterStatusLocal("all");
    setSearchTerm("");
    setIsSearchActive(false);
    // Don't set dataFetched to false to avoid showing loading animation
    // The useEffect will trigger a new fetch due to state changes
  }, [abortController]);

  return (
    <Container className="all-leads-table p-4">
      {/* Show loading animation only for initial fetch */}
      {loading && !dataFetched ? (
        <FetchingDataLoading />
      ) : (
        <>
          <TableControllers
            minimal
            setLoading={setLoading}
            abortController={abortController}
            fetchData={fetchData}
            handleFilterLeads={handleFilterLeads}
            handleSourceFilter={handleSourceFilter}
            handleFilterStatus={handleFilterStatus}
            setAbortController={setAbortController}
            isSearchActive={isSearchActive}
            nocommunication={true}
            onClearAllFilters={handleResetAllFilters}
          />
          <FilterTableNoCommLeadsComponent
            loading={loading}
            data={noCommunicationLeads}
            hideNoData={!dataFetched}
          />
          {/* Pagination visible always */}
          <PaginationComponent />
        </>
      )}
    </Container>
  );
};

export default NoCommunicationLeadsComponent;
