import { useTranslation } from 'react-i18next';
import { Tab, Nav, Row, Col, Form } from "react-bootstrap";
import { useEffect, useState } from "react";
import "./Terms.css";

const TermsPage = () => {
  const { t } = useTranslation();
  const [termsData, setTermsData] = useState([]);
  const [selectedSection, setSelectedSection] = useState('introduction');
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    setTermsData([
      {
        key: 'introduction',
        title: t('terms.overview'),
        navTitle: t('terms.overview'),
        content: t('terms.introduction') // Keep the full content
      },
      {
        key: 'acceptance',
        title: t('terms.acceptance'),
        navTitle: t('terms.acceptance'),
        content: t('terms.acceptanceDetails')
      },
      {
        key: 'account',
        title: t('terms.accountResponsibilities'),
        navTitle: 'Account',
        content: t('terms.accountDetails')
      },
      {
        key: 'conduct',
        title: t('terms.userConduct'),
        navTitle: 'Conduct',
        content: t('terms.conductDetails')
      },
      {
        key: 'property',
        title: t('terms.intellectualProperty'),
        navTitle: 'IP Rights',
        content: t('terms.propertyDetails')
      },
      {
        key: 'disclaimers',
        title: t('terms.disclaimers'),
        navTitle: t('terms.disclaimers'),
        content: t('terms.disclaimersDetails')
      },
      {
        key: 'limitation',
        title: t('terms.limitation'),
        navTitle: 'Liability',
        content: t('terms.limitationDetails')
      },
      {
        key: 'changes',
        title: t('terms.changes'),
        navTitle: 'Changes',
        content: t('terms.changesDetails')
      },
      {
        key: 'termination',
        title: t('terms.termination'),
        navTitle: t('terms.termination'),
        content: t('terms.terminationDetails')
      },
      {
        key: 'governingLaw',
        title: t('terms.governingLaw'),
        navTitle: 'Law',
        content: t('terms.lawDetails')
      },
      {
        key: 'contact',
        title: t('terms.contact'),
        navTitle: t('terms.contact'),
        content: t('terms.contactDetails')
      }
    ]);
  }, [t]);

  const handleSectionChange = (eventKey) => {
    setSelectedSection(eventKey);
  };

  return (
    <div className="terms-container content-container">
      <h1 className="policy-header mb-5">{t('terms.title')}</h1>
      <p className="terms-last-updated">{t('terms.lastUpdated')}</p>

      <Tab.Container 
        defaultActiveKey="introduction" 
        id="terms-tabs"
        activeKey={selectedSection}
        onSelect={handleSectionChange}
      >
        <Row>
          <Col sm={isMobile ? 12 : 3} className="mb-4">
            {isMobile ? (
              <Form.Select 
                value={selectedSection}
                onChange={(e) => handleSectionChange(e.target.value)}
                className="w-100 mb-4"
              >
                {termsData.map((section) => (
                  <option key={section.key} value={section.key}>
                    {section.navTitle}
                  </option>
                ))}
              </Form.Select>
            ) : (
              <Nav variant="pills" className="flex-column terms-navs">
                {termsData.map((section) => (
                  <Nav.Item key={section.key}>
                    <Nav.Link eventKey={section.key}>{section.navTitle}</Nav.Link>
                  </Nav.Item>
                ))}
              </Nav>
            )}
          </Col>
          <Col sm={isMobile ? 12 : 9}>
            <Tab.Content>
              {termsData.map((section) => (
                <Tab.Pane key={section.key} eventKey={section.key}>
                  <h2 className="mb-4">{section.title}</h2>
                  <div className="terms-content">
                    {section.content.split('\n').map((line, i) => (
                      <p key={i}>{line}</p>
                    ))}
                  </div>
                </Tab.Pane>
              ))}
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>
    </div>
  );
};

export default TermsPage;
