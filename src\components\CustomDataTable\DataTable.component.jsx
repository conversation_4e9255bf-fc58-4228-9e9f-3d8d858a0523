import { useMemo } from "react";
import {
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import { Table } from "react-bootstrap";
import { BsFillCaretDownFill } from "react-icons/bs";
import { Link } from "react-router-dom";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import "./datatable.css";

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={`Search ${count} records...`}
    />
  );
}

const DataTableComponent = ({
  columns,
  data,
  loading,
  initialSortBy = [],
  hiddenColumns = [],
}) => {
  const defaultColumn = useMemo(
    () => ({
      // Default Filter UI
      Filter: DefaultColumnFilter,
    }),
    []
  );

  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } =
    useTable(
      {
        columns,
        data,
        defaultColumn,
        initialState: {
          sortBy: initialSortBy,
          hiddenColumns,
        },
      },
      useFilters,
      useGlobalFilter,
      useSortBy,
      usePagination
    );

  return loading ? (
    <FetchingDataLoading className={"content-container"} />
  ) : (
    <>
      <div className={"content-container p-4"}>
        <Table
          responsive
          striped
          bordered
          hover
          className="text-center position-relative"
          {...getTableProps()}
        >
          {loading ? (
            <FetchingDataLoading />
          ) : rows?.length > 0 ? (
            <>
              <thead>
                {headerGroups.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers.map((column, j) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={j}
                      >
                        {column.render("Header")}
                        {/* Render the columns filter UI */}
                        {/*<div>{column.canFilter ? column.render('Filter') : null}</div>*/}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {rows?.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      key={row.original.id}
                    >
                      {row.cells.map((cell, j) => {
                        return (
                          <td
                            {...cell.getCellProps()}
                            key={j}
                            className={`${
                              cell.column.id === "clientId"
                                ? "sticky-column"
                                : ""
                            }`}
                          >
                            {cell.column.id === "Actions" ||
                            cell.column.id === "lastActivity" ||
                            cell.column.id === "assignedTo" ||
                            cell.column.id === "createdAt" ||
                            cell.column.id === "reassign" ? (
                              cell.render("Cell")
                            ) : (
                              <Link to={`/leads/${row.original.id}`}>
                                {cell.render("Cell")}
                              </Link>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </>
          ) : (
            <tbody>
              <tr>
                <td colSpan={columns.length}>
                  <h2 className={"mainColor fw-bold fs-4 text-center"}>
                    {" "}
                    No Leads Found{" "}
                  </h2>
                </td>
              </tr>
            </tbody>
          )}
        </Table>
      </div>
    </>
  );
};

export default DataTableComponent;
