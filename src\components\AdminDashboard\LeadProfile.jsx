import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import DeleteLeadModalContent from "../Modals/DeleteLeadModal";
import AssignTeamModal from "../Modals/AssignTeamModal";
import ActivitiesTab from "../LeadProfile/ActivitiesTab";
import { Col, Nav, Row, Stack, Tab, Button } from "react-bootstrap";
import ProfilePictureComponent from "../ProfilePicture/ProfilePicture.component";
import { IoCaretBack, IoPersonAddOutline } from "react-icons/io5";
import {
  showSuccessToast,
  showErrorToast,
} from "../../utils/toast-success-error";
import { useNavigate, useParams } from "react-router-dom";
import { PiTrashFill } from "react-icons/pi";
import { BiSolidFileExport, BiSolidUserCheck } from "react-icons/bi";
import { useEffect, useState } from "react";
import leadService from "../../services/leads";
import { RiUserLine } from "react-icons/ri";
import EditLeadForm from "../LeadProfile/EditLeadForm";
import AdditionalData from "../LeadProfile/AdditionalData";
import { FaNoteSticky, FaPhone } from "react-icons/fa6";
import { format, parseISO } from "date-fns";
import { RxCalendar, RxQuestionMark } from "react-icons/rx";
import { TbCalendarTime } from "react-icons/tb";
import { useSelector, useDispatch } from "react-redux";
import getAllTeamMembers from "../../services/teams/get-teams.api";
import "../../pages/LeadsProfile/LeadsProfile.css";
import { useTranslation } from "react-i18next";
import { handleResetLead } from "../../redux/features/clientSlice";
import { Tooltip } from "react-tooltip";
import NavigateBackComponent from "../../pages/AdminDashboard/NavigateBack.component";
const LeadProfile = () => {
  const [verticalModalShow, setVerticalModalShow] = useState(false);
  const [leadDetails, setLeadDetails] = useState(null);
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [lastActivityDate, setLastActivityDate] = useState(null);
  const params = useParams();
  const [selectedTeamMember, setSelectedTeamMember] = useState(null);
  const { teamMembers } = useSelector((state) => state.client);
  const { setTeamMembers, setDisableAddMember } = useSelector(
    (state) => state.client
  );
  const { t } = useTranslation();
  const dispatch = useDispatch();
  useEffect(() => {
    const fetchData = async () => {
      const leadsData = await leadService.getSingleLeadApi(params.id, "admin");
      setLeadDetails(leadsData?.data);
      if (leadsData && leadsData.data && leadsData.data.activities) {
        const lastActivity =
          leadsData?.data?.activities[leadsData.data.activities.length - 1];
        if (lastActivity && lastActivity.next_date) {
          setLastActivityDate(lastActivity.next_date);
        } else if (lastActivity && lastActivity.created_at) {
          const parsedDate = parseISO(lastActivity.created_at);
          const formattedDate = format(parsedDate, "yyyy-MM-dd HH:ss");
          setLastActivityDate(formattedDate);
        } else if (lastActivity && lastActivity.note) {
          const dateRegex = /(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/;
          const match = lastActivity.note.match(dateRegex);
          if (match) {
            const extractedDate = match[1];
            setLastActivityDate(extractedDate);
          } else {
            setLastActivityDate(null);
          }
        }
      } else {
        setLastActivityDate(null);
      }
    };
    fetchData();
  }, [params.id]);

  const iconData = [
    {
      Icon: BiSolidFileExport,
      text: t("common.export"),
    },
    {
      Icon: PiTrashFill,
      text: t("common.delete"),
      function: () => setShowCenteredModal(true),
      className: "text-danger",
    },
  ];

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const result = await getAllTeamMembers();
        setTeamMembers(result?.data?.members);
        setDisableAddMember(result?.data?.quota === 0 ? true : false);
      } catch (error) {
        console.log(error);
      }
    };
    fetchTeamMembers();
  }, []);
  const handleSelect = (member) => {
    if (selectedTeamMember && selectedTeamMember.id === member.id)
      setSelectedTeamMember(null);
    else setSelectedTeamMember(member);
  };

  useEffect(() => {
    setSelectedTeamMember(leadDetails?.assigned_to);
  }, [teamMembers]);

  const navigate = useNavigate();

  const parsedDate = leadDetails?.date ? parseISO(leadDetails?.date) : null;
  const parsedCreatedAt = leadDetails?.created_at
    ? parseISO(leadDetails?.created_at)
    : null;
  const formattedCreatedAt = parsedCreatedAt
    ? format(parsedCreatedAt, "yyyy-MM-dd HH:mm")
    : null;
  const formattedDate = parsedDate
    ? format(parsedDate, "yyyy-MM-dd HH:mm")
    : null;

  return (
    <>
      <NavigateBackComponent title={"Back to Leads"} />
      <section className={"content-container lead-profile admin-theme"}>
        <Tab.Container defaultActiveKey={"activities"}>
          <div className={"text-center"}>
            <ProfilePictureComponent />
            <div
              className={
                "my-2 fw-bold d-flex justify-content-center align-content-center"
              }
            >
              <div className={"me-2"}>{leadDetails?.name}</div>
            </div>
            <div className={"opacity-50"}>{leadDetails?.email}</div>
            <Stack
              direction={"horizontal"}
              className={
                "align-content-center align-items-center justify-content-between flex-column flex-md-row"
              }
            >
              <div onClick={() => setVerticalModalShow(true)}>
                <div className={"assign-client-icon"}>
                  <BiSolidUserCheck size={25} />
                </div>
                <div className={"text-nowrap text-white"}>
                  {leadDetails?.assigned_to
                    ? `${t("leadProfile.assignedTo")}: ${
                        leadDetails?.assigned_to?.name
                      }`
                    : "Assign"}
                </div>
              </div>
              <Nav
                variant="pills"
                className={
                  "justify-content-center mx-auto user-profile-tabs my-4"
                }
              >
                <Nav.Item className={"overview-tab"}>
                  <Nav.Link eventKey="overview">Overview</Nav.Link>
                </Nav.Item>
                <Nav.Item className={"activities-tab"}>
                  <Nav.Link eventKey="activities">Activities</Nav.Link>
                </Nav.Item>
              </Nav>
              <Row className={"justify-content-center"}>
                {iconData.map(
                  ({ Icon, text, className, function: onClick }) => (
                    <Col
                      key={text}
                      className={`${className}`}
                      onClick={onClick}
                    >
                      <Icon size={40} className={"profile-icon-container"} />
                      <p className={"text-white"}>{text}</p>
                    </Col>
                  )
                )}
              </Row>
            </Stack>
          </div>

          <Tab.Content>
            <Tab.Pane eventKey="overview">
              <Row className={"overview-details-row"}>
                <Col lg={3}>
                  <div>
                    <RxCalendar size={24} className="mainColor" />
                  </div>
                  <div className={"fw-bold mt-2"}>Created date</div>
                  <div className={"mt-2"}>
                    {formattedDate || formattedCreatedAt}
                  </div>
                </Col>
                <Col lg={3}>
                  <div>
                    <RiUserLine size={24} className="mainColor" />
                  </div>
                  <div className={"fw-bold mt-2 text-uppercase"}>
                    Lead status
                  </div>
                  <div
                    className={`mt-2 ${
                      leadDetails?.status === 0 && "text-warning"
                    } ${leadDetails?.status === 1 && "text-primary"} ${
                      leadDetails?.status === 2 && "text-success"
                    } ${leadDetails?.status === 3 && "text-danger"} ${
                      leadDetails?.status === 4 && "text-danger"
                    } ${leadDetails?.status === 5 && "text-danger"} ${
                      leadDetails?.status === 6 && "text-danger"
                    }`}
                  >
                    {leadDetails?.status === 0 && t("status.pending")}
                    {leadDetails?.status === 1 && t("status.inProgress")}
                    {leadDetails?.status === 11 && t("status.assigned")}
                    {leadDetails?.status === 2 && t("status.accepted")}
                    {leadDetails?.status === 3 && t("status.rejected")}
                    {leadDetails?.status === 4 && t("status.wrongLead")}
                    {leadDetails?.status === 5 && t("status.notQualified")}
                    {leadDetails?.status === 6 && t("status.noCommunication")}
                    {leadDetails?.status === 7 && t("status.booked")}
                    {leadDetails?.status === 8 && t("status.bookedReserved")}
                    {leadDetails?.status === 9 && t("status.canceled")}
                    {leadDetails?.status === 10 && t("status.quotation")}
                  </div>
                </Col>
                <Col lg={3}>
                  <div>
                    <IoPersonAddOutline size={24} className="mainColor" />
                  </div>
                  <div className={"fw-bold mt-2 text-uppercase"}>
                    Created By
                  </div>
                  <div>{leadDetails?.created_by}</div>
                </Col>
                <Col lg={3}>
                  <div>
                    <TbCalendarTime size={24} className="mainColor" />
                  </div>
                  <div className={"fw-bold mt-2"}>Last activity date</div>
                  <div className={"mt-2"}>
                    {lastActivityDate ? lastActivityDate : "N/A"}
                  </div>
                </Col>
              </Row>
              <Row>
                <Col lg={4}>
                  <div className={"about-contact"}>
                    <div className={"fw-bold mainColor fs-6 mb-3"}>
                      About this contact
                    </div>
                    <EditLeadForm
                      leadDetails={leadDetails}
                      setLeadDetails={setLeadDetails}
                    />
                  </div>
                </Col>
                <Col lg={4}>
                  <AdditionalData leadDetails={leadDetails} />
                </Col>
                <Col lg={4}>
                  <div className={"about-contact"}>
                    <div
                      className={
                        "fw-bold mainColor fs-6 d-flex justify-content-between mb-3"
                      }
                    >
                      <div>Notes</div>
                      <div>
                        <FaNoteSticky />
                      </div>
                    </div>
                    <div
                      className={"fs-6 opacity-50"}
                      style={{ maxHeight: "450px", overflowY: "auto" }}
                    >
                      <ul>
                        {leadDetails?.activities?.map((activity) =>
                          activity?.note ? (
                            <li
                              className={"text-start py-2"}
                              style={{
                                borderBottom: "1px solid rgb(204 205 206)",
                              }}
                              key={activity?.id}
                            >
                              {activity?.note}
                            </li>
                          ) : (
                            <li
                              className={"text-start"}
                              style={{
                                borderBottom: "1px solid rgb(204 205 206)",
                              }}
                              key={activity?.id}
                            >
                              {activity?.result}
                              <span>{activity?.next_date}</span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  </div>
                </Col>
              </Row>
              <div className="w-100 d-flex justify-content-end align-content-end mt-4">
                <div className="d-flex align-items-center">
                  <Button
                    type="button"
                    variant={"outline-danger"}
                    id="reset-lead-info"
                    className="rounded-3 py-2 fs-6 ms-3 d-flex align-items-center gap-2"
                    onClick={() => {
                      dispatch(handleResetLead(leadDetails?.id))
                        .unwrap()
                        .then((updatedLead) => {
                          setLeadDetails(updatedLead);
                        });
                    }}
                  >
                    {t("leadProfile.resetLead") || "Reset Lead"}
                    <span className="ms-1">
                      <RxQuestionMark size={16} />
                    </span>
                  </Button>
                  <Tooltip
                    anchorSelect="#reset-lead-info"
                    className="bg-dark text-white"
                    content={
                      t("leadProfile.resetLeadTooltip") ||
                      "Reset the lead to its initial state"
                    }
                  />
                </div>
              </div>
            </Tab.Pane>
            <Tab.Pane eventKey="activities">
              <ActivitiesTab
                leadDetails={leadDetails}
                setLeadDetails={setLeadDetails}
              />
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </section>
      <AssignTeamModal
        setSelectedTeamMember={setSelectedTeamMember}
        selectedTeamMember={selectedTeamMember}
        handleSelect={handleSelect}
        show={verticalModalShow}
        onHide={() => setVerticalModalShow(false)}
        setLeadDetails={setLeadDetails}
        leadDetails={leadDetails}
      />
      <CenteredModal
        show={showCenteredModal}
        children={
          <DeleteLeadModalContent onHide={() => setShowCenteredModal(false)} />
        }
        onHide={() => setShowCenteredModal(false)}
      />
    </>
  );
};

export default LeadProfile;
