import {useEffect, useState, useRef} from "react";
import { Form } from "react-bootstrap";
import { FaMagnifyingGlass } from "react-icons/fa6";
import { IoClose } from "react-icons/io5";
import useDebounce from "../../utils/use-debounce";
import { useTranslation } from "react-i18next";

const SearchComponent = ({ onSearch, placeholder, value, setValue, abortController, setAbortController }) => {
    const [displayValue, setDisplayValue] = useState(value || "");
    const [isSearching, setIsSearching] = useState(false);
    const { t } = useTranslation();
    const debouncedSearchTerm = useDebounce(displayValue, 2000);
    const lastSearchTermRef = useRef(displayValue);

    const handleCancelSearch = () => {
        if (abortController) {
            abortController.abort();
            setAbortController(null);
        }
        setIsSearching(false);
        onSearch(displayValue, null, true);
    };

    const initiateSearch = async (searchTerm) => {
        // Removed minimum length check to allow single character search
        if (!searchTerm) return;

        if (abortController) {
            abortController.abort();
        }

        const newController = new AbortController();
        setAbortController(newController);
        setIsSearching(true);
        lastSearchTermRef.current = searchTerm;

        try {
            await onSearch(searchTerm, newController);
            setIsSearching(false);
        } catch (error) {
            if (error.name === 'AbortError') {
                return;
            }
            console.error('Search error:', error);
        }
    };

    useEffect(() => {
        const searchTerm = debouncedSearchTerm.trim();

        if (searchTerm !== lastSearchTermRef.current && !isSearching) {
            if (searchTerm === '') {
                if (lastSearchTermRef.current !== '') {
                    if (abortController) {
                        abortController.abort();
                        setAbortController(null);
                    }
                    onSearch("", null, true);
                }
                lastSearchTermRef.current = '';
                return;
            }

            // Removed minimum length check here as well
            initiateSearch(searchTerm);
        }
    }, [debouncedSearchTerm]);

    const handleManualSearch = () => {
        const searchTerm = displayValue.trim();
        if (searchTerm === '') {
            handleCancelSearch();
            return;
        }
        initiateSearch(searchTerm);
    };

    const handleSearchChange = (e) => {
        const newValue = e.target.value.trimStart();
        setDisplayValue(newValue);
        if (setValue) {
            setValue(newValue);
        }
    };

    useEffect(() => {
        if (value !== undefined && value !== displayValue) {
            setDisplayValue(value);
        }
    }, [value]);

    const handleKeyDown = (e) => {
        if (e.key === "Enter" && !isSearching) {
            e.preventDefault();
            handleManualSearch();
        } else if (e.key === "Escape" && isSearching) {
            e.preventDefault();
            handleCancelSearch();
        }
    };

    useEffect(() => {
        return () => {
            if (abortController) {
                abortController.abort();
            }
        };
    }, []);

    return (
        <>
            <Form.Group className="position-relative">
                <Form.Control
                    type="text"
                    placeholder={placeholder || t('tableControls.placeholders.searchTable')}
                    value={displayValue}
                    onChange={handleSearchChange}
                    onKeyDown={handleKeyDown}
                    className="rounded-pill mb-2 search-input-rtl"
                    aria-label="Search input"
                    disabled={isSearching}
                />
                <div
                    role="button"
                    className={`mainBgColor searchBadgeRight ${isSearching ? 'searching' : ''}`}
                    onClick={isSearching ? handleCancelSearch : handleManualSearch}
                    aria-label={isSearching ? "Cancel search" : "Search button"}
                    tabIndex={0}
                >
                    {isSearching ? (
                        <IoClose color="white" size={20} />
                    ) : (
                        <FaMagnifyingGlass color="white" size={20} />
                    )}
                </div>
            </Form.Group>
        </>
    );
};

export default SearchComponent;
