.password-input-container:has(input.is-valid) .password-input-icon-TM {
    top: 75%;
}

.password-input-container:has(input.is-invalid) .password-input-icon-TM {
    top: 55%;
}

.password-input-container:has(input.is-invalid) .password-input-icon {
    top: 28%;
}

.password-input-container .form-control.is-valid, .was-validated .form-control:valid, .password-input-container .form-control.is-invalid, .was-validated .form-control:invalid {
    background-image: unset;
}

.password-input-container:has(input.is-valid) .password-input-icon .password-toggle-icon {
    color: #80AA17;
}

.password-input-container:has(input.is-invalid) .password-input-icon .password-toggle-icon {
    color: #F44336;
}

.new-client-btn {
    border-radius: 25px;
    background: linear-gradient(265deg, #92C020 -23.12%, #CAD511 103.2%);
    border: unset;
    width: fit-content;
}

.new-client-btn.dropdown-toggle:after {
    display: none;
}

.clear-filters {
    border-radius: 39px;
    border: 1px solid #92C020;
    background: rgba(156, 196, 29, 0.30);
    color: #000;
}

.data-table-pagination .page-link {
    margin: 10px 5px;
    color: rgba(0, 0, 0, 0.50);
    font-size: 1.25rem;
    font-weight: 400;
    border: unset;
    border-radius: 7px;
}

.data-table-pagination .active>.page-link {
    background: linear-gradient(220deg, #92C020 -9.71%, #CAD511 117.08%);
    color: #FFF;
}

.page-number-input {
    width: fit-content;
    max-width: 50px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #9DC41D;
    background: #FFF;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
    padding: 10px 0 10px 10px;
    margin: 0 5px;
}

.records-buttons-container {
    border: 1px solid #9DC41D;
    border-radius: 5px;
    padding: 4px;
}

.record-button-selected {
    border: 1px solid #9DC41D;
    background: #9DC41D;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.24);
    color: #FFFFFF;
    padding: 6px 10px;
    border-radius: 4px;
}

.record-button {
    border-radius: 4px;
    color: #000;
    padding: 6px 10px;
}

.client-table-row {
    cursor: pointer;
}

tr.client-table-row:hover td {
    background: #F4FFDA;
}

.filter-table-rows a{
    text-decoration: none;
    color: black;
}

.filter-table-rows a:hover:not(.activity-button) {
    color: #92C020;
}

.all-leads-table .table>:not(caption)>*>* {
    vertical-align: middle;
}

.filter-table-rows a:hover:not(.activity-button) {
    color: #92C020;
}

.filter-table-rows a{
    text-decoration: none;
    color: black;
}

.search-input {
    border-radius: 39px;
    border: 1px solid #DFDFDF;
    background: #FAFAFA;
    padding-inline-end: 35px; /* Add padding for the icon */
}

.search-icon {
    position: absolute;
    inset-inline-end: 1.7rem; /* Use logical property instead of right/left */
    top: 50%;
    transform: translate(0, -50%); /* Remove X-axis transform */
    pointer-events: none; /* Ensure the icon doesn't interfere with input */
}

/* Add specific RTL adjustments if needed */
[dir="rtl"] .all-leads-table .search-icon {
    transform: translate(-95%, -50%); /* Ensure consistent transform in RTL */
}

.password-input-icon-TM {
    position: absolute;
    right: 0;
    top: 75%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    color: #00000033;
}

.password-input-container:has(input.is-valid) .password-input-icon-TM {
    top: 75%;
}

.password-input-container:has(input.is-invalid) .password-input-icon-TM {
    top: 55%;
}
