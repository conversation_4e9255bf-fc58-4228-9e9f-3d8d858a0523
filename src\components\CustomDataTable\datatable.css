.admin-theme {
    border-radius: 5px;
    border: 1px solid #444;
    background: #242424;
}

.admin-theme table th, .admin-theme table td {
    color: #FFFFFF;
    border: 1px solid #444;
    background: rgb(36, 36, 36);
    box-shadow: 0 5px 49px -6.18px rgba(0, 0, 0, 0.15);
}

.admin-theme table td a{
    color: #FFFFFF;
}

.admin-theme tr.client-table-row:hover td, .admin-theme tr.client-table-row:hover td a {
    background: #F4FFDA;
    color: #000000;
}

.admin-theme .data-table-pagination .active>.page-link {
    background: #92C020;
    color: #FFFFFF;
}

.admin-theme .record-button {
    color: #FFFFFF;
}

.admin-theme.all-leads-table td {
    padding: 0;
}

.admin-theme.all-leads-table td a {
    width: 100%;
    height: 100%;
    display: block;
    padding: 8px;
}

.data-table-pagination .page-link {
    margin: 10px 5px;
    color: rgba(0, 0, 0, 0.50);
    font-size: 1.25rem;
    font-weight: 400;
    border: unset;
    border-radius: 7px;
}

.data-table-pagination .active>.page-link {
    background: linear-gradient(220deg, #92C020 -9.71%, #CAD511 117.08%);
    color: #FFF;
}

.page-number-input {
    width: fit-content;
    max-width: 50px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #9DC41D;
    background: #FFF;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
    padding: 10px 0 10px 10px;
    margin: 0 5px;
}

.records-buttons-container {
    border: 1px solid #9DC41D;
    border-radius: 5px;
    padding: 4px;
}

.record-button-selected {
    border: 1px solid #9DC41D;
    background: #9DC41D;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.24);
    color: #FFFFFF;
    padding: 6px 10px;
    border-radius: 4px;
}

.record-button {
    border-radius: 4px;
    color: #000;
    padding: 6px 10px;
}

.green-checkbox input {
    border: 1px solid #92BF20;
}

.filter-table-rows a:hover:not(.activity-button) {
    color: #92C020;
}

.admin-theme .table>:not(caption)>*>* {
    vertical-align: middle;
}

.all-leads-table .search-input {
    border-radius: 39px;
    border: 1px solid #DFDFDF;
    background: #FAFAFA;
}

.all-leads-table .search-icon {
    position: absolute;
    right: 2%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.new-client-btn {
    border-radius: 25px;
    background: linear-gradient(265deg, #92C020 -23.12%, #CAD511 103.2%);
    border: unset;
    width: fit-content;
}

.new-client-btn.dropdown-toggle:after {
    display: none;
}

.notification-dropdown {
    color: white;
    position: relative;
}

.members-status-switch .form-check-input {
    min-height: 25px;
    min-width: 50px;
    background-color: #E35757;
    color: white;
}

.members-status-switch.form-switch .form-check-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%27-4 -4 8 8%27%3e%3ccircle r=%273%27 fill=%27%23fff%27/%3e%3c/svg%3e");
}

.members-status-switch .form-check-input:checked {
    background-color: #92C020;
    border-color: #92C020;
}

.members-status-switch .form-switch .form-check-input {
    margin-top: 0;
}

.filter-options-dropdown button:after {
    content: unset;
}

.full-screen-dropdown {
    width: 90vw;
    z-index: 1050; /* Ensure it’s on top of other elements */
    background-color: rgba(255, 255, 255, 0.9); /* Slight transparency */
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.clear-filter, .import-leads, .export-leads, .add-lead {
    margin: 0 10px;
}
