import React, { useState } from "react";
import "./todo-list.css";
import { PiDotsNineLight, PiPencilSimpleLineThin, PiWarning } from "react-icons/pi";
import { FiTrash } from "react-icons/fi";
import Form from "react-bootstrap/Form";
import {FaCheck} from "react-icons/fa";

const TodoList = () => {
    // Dummy data for to-do items
    const [todos, setTodos] = useState([
        { id: 1, text: "Complete this project by Monday", completed: false, date: "2023-12-26 07:15:00" },
        { id: 2, text: "Submit report by Friday", completed: false, date: "2023-12-28 09:30:00" },
        { id: 3, text: "Prepare presentation slides", completed: true, date: "2023-12-30 15:45:00" },
    ]);
    const [editMode, setEditMode] = useState(false);

    const latestTodo = todos.filter(todo => !todo.completed)[0];
    const latestCompletedTodo = todos.filter(todo => todo.completed)[0];

    const handleToggleStatus = (id) => {
        setTodos(prevTodos => prevTodos.map(todo => {
            if (todo.id === id) {
                return { ...todo, completed: !todo.completed };
            }
            return todo;
        }));
    };

    return (
        <div className={"admin-theme h-100 overflow-y-scroll"}>
            <div className={"d-flex justify-content-between align-items-center text-white p-3"}>
                <div className={"fs-6 fw-bold"}>
                    My To Do Items
                </div>
                <div className={"fs-6"}>
                    View All + Add To Do
                </div>
            </div>
            <div className={"my-3"}>
                {todos.map(todo => (
                    <div key={todo.id} className={"text-white px-3"} style={{borderBottom: "0.75px solid #444"}}>
                    {latestTodo && latestTodo.id === todo.id && (
                        <div className={"ms-2 text-warning"}>
                            <PiWarning />
                            {" "}
                            <span>
                            Latest Todo
                        </span>
                        </div>
                    )}
                        {latestCompletedTodo && latestCompletedTodo.id === todo.id && (
                            <div className={"ms-2 text-success"}>
                                <FaCheck />
                                {" "}
                                <span>
                            Latest Finished Todo
                        </span>
                            </div>
                        )}
                        <div className={"my-2 d-flex align-items-center"}>
                            {!todo.completed ? (
                                <>
                                    <PiDotsNineLight className={"me-2"}/>
                                    <div className={"d-flex justify-content-between align-items-start flex-grow-1 flex-column"}>
                                    <Form.Check
                                        type={"checkbox"}
                                        id={`todo-checkbox-${todo.id}`}
                                        label={todo.text}
                                        onChange={()=>handleToggleStatus(todo.id)}
                                        checked={todo.completed}
                                    />
                                    <div style={{ color: "#828690" }}>
                                        {todo.date}
                                    </div>
                                </div>
                                <div role={"button"} style={{ backgroundColor: "rgba(255, 94, 94, 0.05)" }} className={"p-1 rounded-2 mx-2"}>
                                    <FiTrash color={"#FF5E5E"} />
                                </div>
                                <div role={"button"} style={{ backgroundColor: "rgba(146, 192, 32, 0.10)" }} className={"p-1 rounded-2"} onClick={()=> setEditMode(true)}>
                                    <PiPencilSimpleLineThin color={"#92C020"} />
                                </div>
                            </>
                        ) : (
                            <>
                                <PiDotsNineLight className={"me-2"}/>
                                <div className={"d-flex justify-content-between align-items-start flex-grow-1 flex-column"}>
                                    <Form.Check
                                        type={"checkbox"}
                                        id={`todo-checkbox-${todo.id}`}
                                        label={<del>{todo.text}</del>}
                                        onChange={()=>handleToggleStatus(todo.id)}
                                        checked={todo.completed}
                                    />
                                    <div style={{ color: "#828690" }}>
                                        {todo.date}
                                    </div>
                                </div>
                                <div role={"button"} style={{backgroundColor: "rgba(255, 94, 94, 0.05)"}}
                                     className={"p-1 rounded-2 mx-2"}>
                                    <FiTrash color={"#FF5E5E"}/>
                                </div>
                                <div role={"button"} style={{backgroundColor: "rgba(146, 192, 32, 0.10)"}}
                                     className={"p-1 rounded-2"} onClick={()=> setEditMode(true)}>
                                    <PiPencilSimpleLineThin color={"#92C020"}/>
                                </div>
                            </>
                        )}
                    </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default TodoList;